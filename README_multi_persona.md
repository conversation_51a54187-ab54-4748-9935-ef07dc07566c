# 多人格决策系统 (Multi-Persona Decision System)

## 概述

多人格决策系统是一个创新的AI协作框架，通过模拟四个不同专业角色的人格，结合一个决策者角色，来提供更全面、更平衡的决策支持。该系统特别适用于复杂的写作任务、内容优化和创意决策。

## 系统架构

### 四个核心人格

1. **暴躁老哥** 🔥
   - 犀利批评，框架外思维，敢于直言不讳地指出问题和缺陷
   - 核心特质：犀利批评、框架外思维、直言不讳、严格审视
   - 行为准则：毫不留情地批评和质疑，找出所有可能的问题、漏洞、不合理之处
   - 擅长：发现问题、严格批评、确保质量

2. **自省姐** 🤔
   - 深度思考，查漏补缺，善于进行全面的逻辑分析和完整性检查
   - 核心特质：深度思考、查漏补缺、逻辑分析、完整性检查
   - 行为准则：深入思考每个细节，进行全面的逻辑分析，确保没有遗漏任何重要环节
   - 擅长：深度分析、逻辑验证、确保完整

3. **粉丝妹** ✨
   - 发现亮点，放大优势，善于挖掘创意和潜力，提供积极正面的反馈
   - 核心特质：发现亮点、放大优势、挖掘创意、积极鼓励
   - 行为准则：积极地发现亮点和优势，放大好的地方，给予鼓励和支持
   - 擅长：发现优势、鼓励创新、激发潜力

4. **牛马小弟** 🐂
   - 尽职尽责地补全方案中缺失的部分，确保方案完整、可落地
   - 核心特质：方案补全、细节填充、忠诚工具人、执行补全
   - 行为准则：补充逻辑、细节、例子、可行性分析，主动补细节、填空白
   - 擅长：补全方案内容、填补逻辑漏洞、尽责到底

### 决策者角色 👑

- **资深决策者和整合专家**
- 综合分析四个人格的不同视角：
  - 吸收暴躁老哥指出的问题和风险点
  - 采纳自省姐的深度分析和逻辑建议
  - 保留粉丝妹发现的亮点和创意元素
  - 整合牛马小弟补全的细节和可行性方案
- 形成既有创意又严谨、既积极又现实的综合决策

## 主要功能

### 1. 基础决策功能

```python
from multi_persona_decision import MultiPersonaDecisionSystem

# 创建系统实例
mpd_system = MultiPersonaDecisionSystem()

# 执行决策
decision_result = mpd_system.make_decision(
    task_description="如何提高文章的可读性？",
    context="技术文章，目标读者是专业人士"
)

print(f"最终决策: {decision_result.final_decision}")
print(f"置信度: {decision_result.confidence_level}")
```

### 2. 写作决策功能

```python
# 文章重写决策
writing_decision = mpd_system.multi_persona_writing_decision(
    article_segment="原文段落内容",
    original_title="原主题",
    new_title="新主题",
    reference_materials="参考资料"
)
```

### 3. 内容优化决策

```python
# 内容优化
optimization_decision = mpd_system.multi_persona_content_optimization(
    content="待优化内容",
    optimization_goal="提高可读性和专业性",
    constraints="保持原有风格"
)
```

### 4. 结构分析决策

```python
# 文档结构分析
structure_decision = mpd_system.multi_persona_structure_analysis(
    document_content="完整文档内容"
)
```

## 流式API使用

### 基本调用

```python
from backend_apis_streaming import multi_persona_writing_api_streaming

# 流式处理
for log_msg, results in multi_persona_writing_api_streaming(
    input_template='template.docx',
    input_resources_list=['resource1.docx', 'resource2.docx'],
    output_file='output_directory',
    original_title='原始主题',
    new_title='新主题'
):
    print(log_msg, end='')
    if results is not None:
        # 处理完成，获取结果
        print("处理完成！")
        print(json.dumps(results, ensure_ascii=False, indent=2))
        break
```

### 流式输出示例

```
🔧 初始化多人格决策系统完成
📄 加载模板文件: template.docx
📚 添加参考资源: resource1.docx

🧠 开始多人格协作分析...
📋 第一步：多人格文档结构分析...
✅ 结构分析完成，置信度: 0.85
📊 各人格意见汇总:
   🔥 暴躁老哥 (置信度: 0.80): 这结构有问题！逻辑混乱，重点不突出...
   🤔 自省姐 (置信度: 0.90): 需要系统性地重新梳理段落层次关系...
   ✨ 粉丝妹 (置信度: 0.75): 发现了一些很有潜力的亮点内容...
   🐂 牛马小弟 (置信度: 0.85): 补充具体的结构调整方案和实施步骤...

🎯 决策者最终决定: 综合各专家意见，采用层次清晰且富有创意的结构...

✏️ 第二步：多人格内容重写决策...
✅ 重写决策完成，置信度: 0.88
🎯 最终重写方案: 保持逻辑清晰的同时增加创意元素...

✨ 第三步：多人格内容优化决策...
✅ 优化决策完成，置信度: 0.82
🎯 最终优化方案: 在保持专业性的基础上提升可读性...

🎉 多人格协作决策完成！
✅ 处理完成，正在返回结果...
```

## 返回结果结构

```json
{
  "structure_analysis": {
    "final_decision": "结构分析决策",
    "decision_reasoning": "决策推理过程",
    "persona_responses": [...],
    "confidence_level": 0.85
  },
  "writing_decision": {
    "final_decision": "写作决策",
    "decision_reasoning": "决策推理过程",
    "persona_responses": [...],
    "confidence_level": 0.88
  },
  "optimization_decision": {
    "final_decision": "优化决策",
    "decision_reasoning": "决策推理过程",
    "persona_responses": [...],
    "confidence_level": 0.82
  },
  "summary": {
    "original_title": "原主题",
    "new_title": "新主题",
    "overall_confidence": 0.85,
    "processing_timestamp": "2024-06-18T16:05:00"
  }
}
```

## 配置要求

### 1. 环境依赖

确保已安装所需的Python包：
```bash
pip install requests
```

### 2. LLM配置

在 `config.yaml` 中配置LLM API：
```yaml
apis:
  qwen:
    api_key: "your_api_key"
    base_url: "https://api.example.com/v1/chat/completions"
    model_name: "qwen-model"
```

## 测试

运行测试脚本：
```bash
python test_multi_persona.py
```

## 优势特点

1. **多角度分析**: 四个不同人格提供全方位视角
2. **智能整合**: 决策者综合各方意见，避免单一视角局限
3. **高置信度**: 通过多重验证提高决策质量
4. **流式处理**: 实时反馈处理进度
5. **灵活应用**: 适用于多种写作和决策场景

## 应用场景

- 📝 文章重写和主题转换
- ✨ 内容优化和润色
- 📋 文档结构分析
- 🎯 创意决策支持
- 📊 多维度评估

## 注意事项

1. 确保LLM API配置正确
2. 处理大文档时注意API调用频率限制
3. 根据实际需求调整人格特质和提示词
4. 建议在正式使用前进行充分测试
