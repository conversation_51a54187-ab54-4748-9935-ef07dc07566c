#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多人格决策系统
包含四个人格角色和一个决策者，用于协作完成写作任务
"""

import os
import json
import re
import tempfile
import shutil
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from dataclasses import dataclass, field
from llm_helper import LLMAPIClient
from config import ConfigManager
from document import DocumentObject, DocumentParser


@dataclass
class PersonaResponse:
    """
    人格响应结果
    """
    persona_name: str                       # 人格名称
    response_content: str                   # 响应内容
    confidence_score: float                 # 置信度分数 (0-1)
    reasoning: str                          # 推理过程
    suggestions: List[str] = field(default_factory=list)  # 建议列表
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'persona_name': self.persona_name,
            'response_content': self.response_content,
            'confidence_score': self.confidence_score,
            'reasoning': self.reasoning,
            'suggestions': self.suggestions
        }


@dataclass
class DecisionResult:
    """
    决策结果
    """
    final_decision: str                     # 最终决策
    decision_reasoning: str                 # 决策推理
    persona_responses: List[PersonaResponse] # 各人格响应
    integration_process: str                # 整合过程
    confidence_level: float                 # 整体置信度
    metadata: Dict[str, Any] = field(default_factory=dict)  # 元数据
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'final_decision': self.final_decision,
            'decision_reasoning': self.decision_reasoning,
            'persona_responses': [p.to_dict() for p in self.persona_responses],
            'integration_process': self.integration_process,
            'confidence_level': self.confidence_level,
            'metadata': self.metadata
        }


class MultiPersonaDecisionSystem:
    """
    多人格决策系统主类
    """
    
    def __init__(self, temp_base_dir: Optional[str] = None, llm_client: Optional[LLMAPIClient] = None):
        """
        初始化多人格决策系统
        
        Args:
            temp_base_dir: 临时文件基础目录
            llm_client: LLM客户端，如果不提供则从配置文件创建
        """
        self.temp_base_dir = temp_base_dir or tempfile.gettempdir()
        self.template: Optional[DocumentObject] = None
        self.resources: List[DocumentObject] = []
        self._temp_dirs: List[str] = []  # 跟踪创建的临时目录
        
        # 初始化LLM客户端
        if llm_client:
            self.llm_client = llm_client
        else:
            try:
                config = ConfigManager()
                api_key = config.get('apis.qwen.api_key')
                base_url = config.get('apis.qwen.base_url')
                self.llm_client = LLMAPIClient(api_key, base_url)
            except Exception as e:
                print(f"警告: 无法初始化LLM客户端: {e}")
                self.llm_client = None
        
        # 定义四个人格
        self.personas = {
            "暴躁老哥": {
                "role": "暴躁老哥",
                "description": "犀利批评，框架外思维，敢于直言不讳地指出问题和缺陷，不留情面地进行严格审视",
                "traits": ["犀利批评", "框架外思维", "直言不讳", "严格审视"],
                "rule": "你要毫不留情地批评和质疑，找出所有可能的问题、漏洞、不合理之处。你的使命是当那个说真话的人，即使真话刺耳。不要客气，不要妥协，要犀利、直接、暴躁一点。",
                "goal": "发现问题，严格批评，确保质量"
            },
            "自省姐": {
                "role": "自省姐",
                "description": "深度思考，查漏补缺，善于进行全面的逻辑分析和完整性检查，确保方案的严谨性",
                "traits": ["深度思考", "查漏补缺", "逻辑分析", "完整性检查"],
                "rule": "你要深入思考每个细节，进行全面的逻辑分析。你的任务是查漏补缺，确保没有遗漏任何重要环节。要系统性地思考，从多个维度进行分析。",
                "goal": "深度分析，逻辑验证，确保完整"
            },
            "粉丝妹": {
                "role": "粉丝妹",
                "description": "发现亮点，放大优势，善于挖掘创意和潜力，提供积极正面的反馈和鼓励",
                "traits": ["发现亮点", "放大优势", "挖掘创意", "积极鼓励"],
                "rule": "你要积极地发现亮点和优势，放大好的地方，给予鼓励和支持。你的使命是挖掘潜力，激发创意，让人感到被认可和鼓舞。",
                "goal": "发现优势，鼓励创新，激发潜力"
            },
            "牛马小弟": {
                "role": "牛马小弟",
                "description": "尽职尽责地帮老板补全方案中缺失的部分，包括补充逻辑、细节、例子、可行性分析，确保方案完整、可落地",
                "traits": ["方案补全", "细节填充", "忠诚工具人", "执行补全"],
                "rule": "你要尽职尽责地帮老板补全方案中缺失的部分，包括补充逻辑、细节、例子、可行性分析，确保方案完整、可落地。你不能偷懒，也不能讲废话，要实用、清晰，始终以'补全'为目标。如果发现老板写的不够清楚，要主动补细节、填空白。",
                "goal": "补全方案内容，填补逻辑漏洞，尽责到底"
            }
        }
    
    def set_template(self, file_path: str) -> DocumentObject:
        """设置模板文档"""
        temp_dir = tempfile.mkdtemp(prefix="template_", dir=self.temp_base_dir)
        self._temp_dirs.append(temp_dir)
        
        self.template = DocumentParser.parse_document(file_path, temp_dir)
        return self.template
    
    def add_resource(self, file_path: str) -> DocumentObject:
        """添加资源文档"""
        temp_dir = tempfile.mkdtemp(prefix="resource_", dir=self.temp_base_dir)
        self._temp_dirs.append(temp_dir)
        
        resource_doc = DocumentParser.parse_document(file_path, temp_dir)
        self.resources.append(resource_doc)
        return resource_doc
    
    def cleanup(self):
        """清理临时文件"""
        for temp_dir in self._temp_dirs:
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except Exception as e:
                    print(f"警告: 无法删除临时目录 {temp_dir}: {e}")
        self._temp_dirs.clear()
    
    def __del__(self):
        """析构函数，自动清理临时文件"""
        self.cleanup()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理"""
        self.cleanup()
    
    # ==================== 人格提示词模板 ====================
    
    def get_persona_prompt(self, persona_name: str, task_description: str, context: str) -> str:
        """
        获取特定人格的提示词

        Args:
            persona_name: 人格名称
            task_description: 任务描述
            context: 上下文信息

        Returns:
            str: 格式化的提示词
        """
        persona_info = self.personas[persona_name]

        prompt = f"""你是{persona_info['role']}，{persona_info['description']}。

你的行为准则：{persona_info['rule']}

你的目标：{persona_info['goal']}

你的核心特质：{', '.join(persona_info['traits'])}

现在请基于你的人格特色，对以下任务提供你的专业意见：

任务描述：
{task_description}

上下文信息：
{context}

请按照以下JSON格式回复：
{{
    "response_content": "你的具体回应和建议（要体现出{persona_name}的鲜明特色）",
    "confidence_score": 0.85,
    "reasoning": "你的推理过程和依据（要符合你的人格特点）",
    "suggestions": ["具体建议1", "具体建议2", "具体建议3"]
}}

记住：你必须严格按照{persona_name}的人格特色来回应，不要偏离你的角色定位！"""

        return prompt
    
    def get_decision_maker_prompt(self, task_description: str, persona_responses: List[PersonaResponse]) -> str:
        """
        获取决策者的提示词
        
        Args:
            task_description: 任务描述
            persona_responses: 各人格的响应
            
        Returns:
            str: 决策者提示词
        """
        responses_text = ""
        for response in persona_responses:
            responses_text += f"\n{response.persona_name}的意见：\n"
            responses_text += f"内容：{response.response_content}\n"
            responses_text += f"置信度：{response.confidence_score}\n"
            responses_text += f"推理：{response.reasoning}\n"
            responses_text += f"建议：{', '.join(response.suggestions)}\n"
            responses_text += "-" * 50 + "\n"
        
        prompt = f"""你是一位资深的决策者和整合专家，具有丰富的跨领域经验和卓越的判断能力。
你的任务是综合分析四位人格的意见，做出最优的决策。

原始任务：
{task_description}

四位人格的意见如下：
{responses_text}

现在你需要整合这四个人格的不同视角：
- 暴躁老哥：犀利批评，找出问题和风险
- 自省姐：深度思考，逻辑分析和完整性检查
- 粉丝妹：发现亮点，挖掘优势和创意
- 牛马小弟：补全细节，确保方案可落地

请综合考虑所有人格的意见，权衡各种因素，做出最符合任务要求的决策。

请按照以下JSON格式回复：
{{
    "final_decision": "你的最终决策内容（要平衡四个人格的观点）",
    "decision_reasoning": "详细说明你的决策依据和整合过程",
    "integration_process": "描述你如何平衡暴躁老哥的批评、自省姐的分析、粉丝妹的鼓励和牛马小弟的补全",
    "confidence_level": 0.90
}}

请确保你的决策：
1. 充分吸收了暴躁老哥指出的问题和风险点
2. 采纳了自省姐的深度分析和逻辑建议
3. 保留了粉丝妹发现的亮点和创意元素
4. 整合了牛马小弟补全的细节和可行性方案
5. 形成一个既有创意又严谨、既积极又现实的综合决策"""
        
        return prompt

    # ==================== 核心功能方法 ====================

    def safe_json_loads(self, json_str: str) -> dict:
        """安全的JSON解析函数"""
        try:
            # 首先尝试直接解析
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            print(f"   ⚠️  JSON解析失败: {e}")
            try:
                # 尝试提取JSON部分
                json_match = re.search(r'\{.*\}', json_str, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    return json.loads(json_str)
                else:
                    print(f"   ⚠️  未找到有效的JSON格式")
                    return {}
            except json.JSONDecodeError as e2:
                print(f"   ⚠️  清理后仍然解析失败: {e2}")
                return {}

    def query_persona(self, persona_name: str, task_description: str, context: str) -> PersonaResponse:
        """
        查询特定人格的意见

        Args:
            persona_name: 人格名称
            task_description: 任务描述
            context: 上下文信息

        Returns:
            PersonaResponse: 人格响应结果
        """
        if not self.llm_client:
            raise ValueError("LLM客户端未初始化")

        prompt = self.get_persona_prompt(persona_name, task_description, context)

        try:
            response = self.llm_client.chat(prompt)
            response_content = response['choices'][0]['message']['content'].strip()

            # 解析JSON响应
            response_data = self.safe_json_loads(response_content)

            return PersonaResponse(
                persona_name=persona_name,
                response_content=response_data.get('response_content', ''),
                confidence_score=float(response_data.get('confidence_score', 0.5)),
                reasoning=response_data.get('reasoning', ''),
                suggestions=response_data.get('suggestions', [])
            )

        except Exception as e:
            print(f"   ⚠️  查询{persona_name}失败: {e}")
            return PersonaResponse(
                persona_name=persona_name,
                response_content=f"查询失败: {str(e)}",
                confidence_score=0.0,
                reasoning="系统错误",
                suggestions=[]
            )

    def make_decision(self, task_description: str, context: str) -> DecisionResult:
        """
        执行多人格决策过程

        Args:
            task_description: 任务描述
            context: 上下文信息

        Returns:
            DecisionResult: 决策结果
        """
        if not self.llm_client:
            raise ValueError("LLM客户端未初始化")

        # 第一步：收集各人格的意见
        persona_responses = []
        for persona_name in self.personas.keys():
            response = self.query_persona(persona_name, task_description, context)
            persona_responses.append(response)

        # 第二步：决策者整合意见
        decision_prompt = self.get_decision_maker_prompt(task_description, persona_responses)

        try:
            decision_response = self.llm_client.chat(decision_prompt)
            decision_content = decision_response['choices'][0]['message']['content'].strip()

            # 解析决策结果
            decision_data = self.safe_json_loads(decision_content)

            return DecisionResult(
                final_decision=decision_data.get('final_decision', ''),
                decision_reasoning=decision_data.get('decision_reasoning', ''),
                persona_responses=persona_responses,
                integration_process=decision_data.get('integration_process', ''),
                confidence_level=float(decision_data.get('confidence_level', 0.5)),
                metadata={
                    'timestamp': datetime.now().isoformat(),
                    'task_description': task_description,
                    'context_length': len(context)
                }
            )

        except Exception as e:
            print(f"   ⚠️  决策整合失败: {e}")
            return DecisionResult(
                final_decision="决策失败",
                decision_reasoning=f"系统错误: {str(e)}",
                persona_responses=persona_responses,
                integration_process="决策过程中断",
                confidence_level=0.0,
                metadata={'error': str(e)}

    # ==================== 写作专门方法 ====================

    def multi_persona_writing_decision(self, article_segment: str, original_title: str, new_title: str,
                                     reference_materials: str = "") -> DecisionResult:
        """
        多人格写作决策：针对文章段落重写任务

        Args:
            article_segment: 文章段落
            original_title: 原始主题
            new_title: 新主题
            reference_materials: 参考资料

        Returns:
            DecisionResult: 写作决策结果
        """
        task_description = f"""
请对以下文章段落进行重写，将主题从"{original_title}"转换为"{new_title}"。
需要保持文章的结构和风格，但要确保内容与新主题相符。

原文段落：
{article_segment}
"""

        context = f"""
原始主题：{original_title}
新主题：{new_title}
参考资料：
{reference_materials}
"""

        return self.make_decision(task_description, context)

    def multi_persona_content_optimization(self, content: str, optimization_goal: str,
                                         constraints: str = "") -> DecisionResult:
        """
        多人格内容优化决策

        Args:
            content: 待优化内容
            optimization_goal: 优化目标
            constraints: 约束条件

        Returns:
            DecisionResult: 优化决策结果
        """
        task_description = f"""
请对以下内容进行优化，优化目标是：{optimization_goal}

待优化内容：
{content}
"""

        context = f"""
优化目标：{optimization_goal}
约束条件：{constraints}
内容长度：{len(content)}字符
"""

        return self.make_decision(task_description, context)

    def multi_persona_structure_analysis(self, document_content: str) -> DecisionResult:
        """
        多人格文档结构分析

        Args:
            document_content: 文档内容

        Returns:
            DecisionResult: 结构分析决策结果
        """
        task_description = f"""
请分析以下文档的结构，提供合理的段落划分建议和结构优化方案。

文档内容：
{document_content}
"""

        context = f"""
文档长度：{len(document_content)}字符
分析目标：结构优化和段落划分
"""

        return self.make_decision(task_description, context)
            )
