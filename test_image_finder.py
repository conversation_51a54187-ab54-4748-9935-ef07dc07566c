#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试图片查找功能的脚本
"""

import os
import glob


def find_matching_image_files(txt_file_path):
    """
    查找与txt文件同名的图片文件
    
    Args:
        txt_file_path: txt文件的完整路径
        
    Returns:
        list: 匹配的图片文件路径列表
    """
    # 支持的图片格式
    image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.tif', '.webp']
    
    # 获取txt文件的目录和基础文件名（不含扩展名）
    file_dir = os.path.dirname(txt_file_path)
    base_name = os.path.splitext(os.path.basename(txt_file_path))[0]
    
    matching_images = []
    
    # 遍历所有支持的图片格式
    for ext in image_extensions:
        image_path = os.path.join(file_dir, base_name + ext)
        if os.path.exists(image_path):
            matching_images.append(image_path)
    
    return matching_images


def find_matching_image_files_by_name(file_name, search_directory=None):
    """
    根据文件名查找同名的图片文件（不需要完整路径）
    
    Args:
        file_name: 文件名（如 "document.txt"）
        search_directory: 搜索目录，如果为None则使用文件名所在目录
        
    Returns:
        list: 匹配的图片文件路径列表
    """
    # 支持的图片格式
    image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.tif', '.webp']
    
    # 获取基础文件名（不含扩展名）
    base_name = os.path.splitext(file_name)[0]
    
    # 如果没有指定搜索目录，使用当前目录
    if search_directory is None:
        search_directory = os.path.dirname(file_name) if os.path.dirname(file_name) else '.'
    
    matching_images = []
    
    # 遍历所有支持的图片格式
    for ext in image_extensions:
        image_path = os.path.join(search_directory, base_name + ext)
        if os.path.exists(image_path):
            matching_images.append(image_path)
    
    return matching_images


def test_image_finder():
    """
    测试图片查找功能
    """
    print("🧪 测试图片查找功能")
    print("=" * 50)
    
    # 测试目录
    test_dirs = [
        'asset/报案平台资料审核素材2/报案平台资料审核素材/中联硅谷（北京）股权投资基金管理有限公司/liushui',
        'asset/报案平台资料审核素材2/报案平台资料审核素材/安晓博等人（和信系）1/liushui',
        'asset/报案平台资料审核素材2/报案平台资料审核素材/安晓博等人（和信系）2/liushui',
    ]
    
    for test_dir in test_dirs:
        if not os.path.exists(test_dir):
            print(f"⚠️ 目录不存在: {test_dir}")
            continue
            
        print(f"\n📁 测试目录: {test_dir}")
        
        # 查找所有txt文件
        txt_files = glob.glob(os.path.join(test_dir, "*.txt"))
        
        if not txt_files:
            print(f"  ⚠️ 没有找到txt文件")
            continue
            
        print(f"  📄 找到 {len(txt_files)} 个txt文件")
        
        for txt_file in txt_files[:3]:  # 只测试前3个文件
            file_name = os.path.basename(txt_file)
            print(f"\n  📝 测试文件: {file_name}")
            
            # 方法1：使用完整路径
            matching_images1 = find_matching_image_files(txt_file)
            print(f"    方法1 - 找到 {len(matching_images1)} 个匹配图片:")
            for img in matching_images1:
                print(f"      🖼️ {os.path.basename(img)}")
            
            # 方法2：使用文件名和目录
            matching_images2 = find_matching_image_files_by_name(file_name, test_dir)
            print(f"    方法2 - 找到 {len(matching_images2)} 个匹配图片:")
            for img in matching_images2:
                print(f"      🖼️ {os.path.basename(img)}")
            
            # 检查结果是否一致
            if set(matching_images1) == set(matching_images2):
                print(f"    ✅ 两种方法结果一致")
            else:
                print(f"    ❌ 两种方法结果不一致")


if __name__ == "__main__":
    test_image_finder()
