#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流式版本的后端API - 支持实时日志更新
"""

import os
import json
from config import ConfigManager
from llm_helper import LLMAPIClient
from template_driven_writing import TemplateDrivenWriting
from multi_persona_decision import MultiPersonaDecisionSystem


def _create_default_llm_client(llm_source='glm') -> LLMAPIClient:
    """创建默认的LLM客户端"""
    config_manager = ConfigManager()

    api_key = config_manager.get(f'apis.{llm_source}.api_key')
    base_url = config_manager.get(f'apis.{llm_source}.base_url')
    model_name = config_manager.get(f'apis.{llm_source}.model_name')

    return LLMAPIClient(api_key, model_name=model_name, base_url=base_url)



def sie_api_streaming(input_file_list,output_file , config_info=''):
    """
    流式版本的结构化信息提取API

    Args:
        input_file_list: 输入文件列表
        config_info: 配置信息
        output_file: 输出文件路径

    Yields:
        tuple: (log_message, results) - results为None表示还在处理中，不为None表示处理完成
    """
    try:
        config = ConfigManager()
        # 确保输出目录存在
        os.makedirs(output_file, exist_ok=True)

        # 初始化LLM客户端
        llm_client = _create_default_llm_client()
        yield "🔧 初始化LLM客户端完成\n", None

    except Exception as e:
        yield f'\n❌ 处理过程中发生错误：{str(e)}\n', None
        return


def tdw_api_streaming(input_template, input_resources_list, output_file, original_title, new_title):
    """
    流式版本的模板驱动写作API

    Args:
        input_template: 模板文件路径
        input_resources_list: 参考资源文件路径列表
        output_file: 输出目录
        original_title: 原始标题
        new_title: 新标题

    Yields:
        tuple: (log_message, results) - results为None表示还在处理中，不为None表示处理完成
    """
    try:
        config = ConfigManager()
        # 确保输出目录存在
        os.makedirs(output_file, exist_ok=True)

        # 初始化LLM客户端
        llm_client = _create_default_llm_client()
        tdw_obj = TemplateDrivenWriting(temp_base_dir=output_file, llm_client=llm_client)

        yield "🔧 初始化LLM客户端完成\n", None

        tdw_obj.set_template(input_template)
        yield f"📄 加载模板文件: {os.path.basename(input_template)}\n", None

        for input_resources in input_resources_list:
            tdw_obj.add_resource(input_resources)
            yield f"📚 添加参考资源: {os.path.basename(input_resources)}\n", None

        # 第一步：切割模版文档
        yield "\n🔍 第一步：分析文章结构...\n", None
        summary = ''
        partitions = []
        retry_count = 0
        while (len(partitions) == 0 or summary == '') and retry_count < 3:  # 如果解析错误，重试一下
            retry_count = retry_count + 1
            yield f'📝 第{retry_count}次尝试解析文章结构...\n', None
            summary, partitions = tdw_obj.partitions()

        if len(partitions) == 0 or summary == '':
            yield '❌ 解析文章结构失败，请检查文章格式是否正确\n', None
            return

        yield f'✅ 文章摘要：{summary}\n', None
        yield f'✅ 文章结构解析成功，共{len(partitions)}个段落\n', None

        for i, partition in enumerate(partitions):
            yield f'   📋 段落{i+1}：{partition["summary"]}\n', None

        new_paragraphs = []
        # 第二~四步：每个段落串行处理
        yield f"\n🔄 开始处理各段落（共{len(partitions)}个）...\n", None

        for i, partition in enumerate(partitions):
            yield f'\n📝 开始处理第{i+1}个段落...\n', None

            if original_title != new_title:
                # 第二步： 生成写作指导
                retry_count = 0
                instruction = ''
                warnings = ''
                while instruction == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   🎯 第{retry_count}次尝试生成写作指导...\n', None
                    instruction, warnings = tdw_obj.object_replace(partition['slice'], original_title, new_title)

                if instruction == '':
                    yield f'   ❌ 段落{i+1}生成写作指导失败，跳过该段落\n', None
                    continue

                if isinstance(instruction, dict):
                    instruction = json.dumps(instruction, ensure_ascii=False, indent=2)
                if isinstance(warnings, dict):
                    warnings = json.dumps(warnings, ensure_ascii=False, indent=2)

                yield f'   ✅ 写作指导生成成功\n', None
                yield f'   📋 写作指导：{instruction}...\n', None
                yield f'   ⚠️  注意事项：{warnings}...\n', None

                # 第三步： 搜索参考内容
                yield f'   🔍 开始搜索参考内容...\n', None
                ref = []
                retry_count = 0
                while len(ref) == 0 and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   🔍 第{retry_count}次尝试搜索参考内容...\n', None
                    ref = tdw_obj.search(instruction)

                if len(ref) == 0:
                    yield f'   ❌ 段落{i+1}搜索参考内容失败，跳过该段落\n', None
                    continue

                yield f'   ✅ 搜索到{len(ref)}个相关片段\n', None

                # 将搜索到的段落列表转换为字符串，只包含段落内容
                ref_text = "'''" + "'''\n\n'''".join([item['text'] for item in ref if item['text'] is not None]) + "'''"

                # 第3.2步： 段落重写指导
                retry_count = 0
                rewrite_instruction = ''
                while rewrite_instruction == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   📝 第{retry_count}次尝试生成重写指导...\n', None
                    rewrite_instruction = tdw_obj.rewrite_instruction(partition['summary'], original_title, new_title, ref_text, instruction, warnings)

                if rewrite_instruction == '':
                    yield f'   ❌ 段落{i+1}重写写作指导失败，跳过该段落\n', None
                    continue

                if isinstance(rewrite_instruction, dict):
                    rewrite_instruction = json.dumps(rewrite_instruction, ensure_ascii=False, indent=2)

                yield f'   ✅ 重写指导生成成功\n', None

                # 第四步： 段落重写
                retry_count = 0
                new_paragraph = ''
                while new_paragraph == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   ✏️  第{retry_count}次尝试段落重写...\n', None
                    new_paragraph = tdw_obj.rewrite(partition['slice'], ref_text, instruction)

                if new_paragraph == '':
                    yield f'   ❌ 段落{i+1}重写失败，跳过该段落\n', None
                    continue

                yield f'   ✅ 段落{i+1}重写成功\n', None
                yield f'   📄 重写后段落预览：{new_paragraph}...\n', None
                new_paragraphs.append({'seq_no': i, 'paragraph': new_paragraph})
            else:
                # 第三步： 搜索参考内容
                ref = []
                retry_count = 0
                while len(ref) == 0 and retry_count < 3:
                    retry_count = retry_count + 1
                    ref = tdw_obj.search(partition['summary'])
                if len(ref) == 0:
                    yield f'   ❌ 段落{i+1}搜索参考内容失败，跳过该段落\n', None
                    continue
                yield f'   ✅ 搜索到{len(ref)}个相关片段\n', None

                # 将搜索到的段落列表转换为字符串，只包含段落内容
                ref_text = "'''" + "'''\n\n'''".join([item['text'] for item in ref if item['text'] is not None]) + "'''"

                # 第四步： 段落重写
                retry_count = 0
                new_paragraph = ''
                while new_paragraph == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    new_paragraph = tdw_obj.rewrite(partition['slice'], ref_text,
                                                    '根据素材对片段进行优化，补充缺失的内容，修复错误的信息')

                if new_paragraph == '':
                    yield f'   ❌ 段落{i+1}重写失败，跳过该段落\n', None
                    continue

                yield f'   ✅ 段落{i+1}重写成功\n', None
                yield f'   📄 重写后段落预览：{new_paragraph}...\n', None
                new_paragraphs.append({'seq_no': i, 'paragraph': new_paragraph})

        if len(new_paragraphs) == 0:
            yield '\n❌ 所有段落处理失败，无法生成文章\n', None
            return

        # 第五步：段落聚合
        yield f'\n📑 第五步：开始段落聚合（共{len(new_paragraphs)}个段落）...\n', None
        paragraphs_text = "\n\n".join([f"段落 {item['seq_no'] + 1}:\n{item['paragraph']}\n\n" for item in
                                       sorted(new_paragraphs, key=lambda x: x['seq_no'])
                                       if item['paragraph'] is not None])
        paragraphs_text_printout = "\n\n".join([f"{item['paragraph']}\n\n" for item in
                                                sorted(new_paragraphs, key=lambda x: x['seq_no'])
                                                if item['paragraph'] is not None])

        yield '✅ 段落聚合前准备完成\n', None

        new_article = tdw_obj.para_merge(paragraphs_text)
        yield '✅ 文章聚合成功\n', None

        # 第六步：文章润色
        yield '\n✨ 第六步：开始文章润色...\n', None
        polished = tdw_obj.polish(new_article)
        yield '✅ 文章润色成功\n', None

        yield '\n🎉 所有处理步骤完成！\n', None

        # 返回最终结果
        yield '✅ 处理完成，正在返回结果...\n', (str(paragraphs_text_printout), str(new_article), str(polished))

    except Exception as e:
        yield f'\n❌ 处理过程中发生错误：{str(e)}\n', None
        return


def multi_persona_writing_api_streaming(input_template, input_resources_list, output_file, original_title, new_title):
    """
    多人格写作决策流式API

    Args:
        input_template: 模板文件路径
        input_resources_list: 参考资源文件路径列表
        output_file: 输出目录
        original_title: 原始标题
        new_title: 新标题

    Yields:
        tuple: (log_message, results) - results为None表示还在处理中，不为None表示处理完成
    """
    try:
        config = ConfigManager()
        # 确保输出目录存在
        os.makedirs(output_file, exist_ok=True)

        # 初始化LLM客户端和多人格决策系统
        llm_client = _create_default_llm_client()
        mpd_system = MultiPersonaDecisionSystem(temp_base_dir=output_file, llm_client=llm_client)

        yield "🔧 初始化多人格决策系统完成\n", None

        # 设置模板和资源
        mpd_system.set_template(input_template)
        yield f"📄 加载模板文件: {os.path.basename(input_template)}\n", None

        for input_resources in input_resources_list:
            mpd_system.add_resource(input_resources)
            yield f"📚 添加参考资源: {os.path.basename(input_resources)}\n", None

        # 获取模板内容
        template_content = mpd_system.template.content
        reference_content = "\n\n".join([resource.content for resource in mpd_system.resources])

        yield "\n🧠 开始多人格协作分析...\n", None

        # 第一步：文档结构分析
        yield "📋 第一步：多人格文档结构分析...\n", None
        structure_decision = mpd_system.multi_persona_structure_analysis(template_content)

        yield f"✅ 结构分析完成，置信度: {structure_decision.confidence_level:.2f}\n", None
        yield f"📊 各人格意见汇总:\n", None

        persona_icons = {"暴躁老哥": "🔥", "自省姐": "🤔", "粉丝妹": "✨", "牛马小弟": "🐂"}
        for persona_response in structure_decision.persona_responses:
            icon = persona_icons.get(persona_response.persona_name, "🎭")
            yield f"   {icon} {persona_response.persona_name} (置信度: {persona_response.confidence_score:.2f}): {persona_response.response_content[:100]}...\n", None

        yield f"🎯 决策者最终决定: {structure_decision.final_decision[:200]}...\n", None

        # 第二步：内容重写决策
        yield "\n✏️ 第二步：多人格内容重写决策...\n", None
        writing_decision = mpd_system.multi_persona_writing_decision(
            template_content, original_title, new_title, reference_content
        )

        yield f"✅ 重写决策完成，置信度: {writing_decision.confidence_level:.2f}\n", None
        yield f"📊 各人格重写建议:\n", None

        for persona_response in writing_decision.persona_responses:
            icon = persona_icons.get(persona_response.persona_name, "🎭")
            yield f"   {icon} {persona_response.persona_name}: {persona_response.response_content[:150]}...\n", None
            if persona_response.suggestions:
                yield f"      💡 建议: {', '.join(persona_response.suggestions[:2])}...\n", None

        yield f"🎯 最终重写方案: {writing_decision.final_decision[:300]}...\n", None

        # 第三步：内容优化决策
        yield "\n✨ 第三步：多人格内容优化决策...\n", None
        optimization_goal = f"将内容从'{original_title}'主题转换为'{new_title}'主题，保持文章质量和可读性"
        optimization_decision = mpd_system.multi_persona_content_optimization(
            writing_decision.final_decision, optimization_goal, "保持原文风格和结构"
        )

        yield f"✅ 优化决策完成，置信度: {optimization_decision.confidence_level:.2f}\n", None
        yield f"🎯 最终优化方案: {optimization_decision.final_decision[:300]}...\n", None

        yield "\n🎉 多人格协作决策完成！\n", None

        # 整理最终结果
        final_results = {
            'structure_analysis': structure_decision.to_dict(),
            'writing_decision': writing_decision.to_dict(),
            'optimization_decision': optimization_decision.to_dict(),
            'summary': {
                'original_title': original_title,
                'new_title': new_title,
                'overall_confidence': (structure_decision.confidence_level +
                                     writing_decision.confidence_level +
                                     optimization_decision.confidence_level) / 3,
                'processing_timestamp': structure_decision.metadata.get('timestamp', '')
            }
        }

        # 返回最终结果
        yield '✅ 处理完成，正在返回结果...\n', final_results

    except Exception as e:
        yield f'\n❌ 多人格决策过程中发生错误：{str(e)}\n', None
        return


if __name__ == '__main__':
    # 测试流式API
    input_template = 'asset/天津市债务融资工具总体情况.docx'
    input_resources_list = ['asset/北京市债务融资工具发展报告：历史演进、2024年现状与未来展望.docx']
    output_file = 'output'
    original_title = '天津市'
    new_title = '北京市'

    for log_msg, results in tdw_api_streaming(input_template, input_resources_list, output_file, original_title, new_title):
        print(log_msg, end='')
        if results is not None:
            print("处理完成！")
            break
