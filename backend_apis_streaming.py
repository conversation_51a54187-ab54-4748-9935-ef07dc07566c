#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流式版本的后端API - 支持实时日志更新
"""

import os
import json
import zipfile
import tempfile
import shutil
import glob
import base64
from config import ConfigManager
from llm_helper import LLMAPIClient
from template_driven_writing import TemplateDrivenWriting
from multi_persona_decision import MultiPersonaDecisionSystem
from img_parser import parse_img_document_by_paddleocr
from source_struct_loader import SourceStructLoader


def _create_default_llm_client(llm_source='glm') -> LLMAPIClient:
    """创建默认的LLM客户端"""
    config_manager = ConfigManager()

    api_key = config_manager.get(f'apis.{llm_source}.api_key')
    base_url = config_manager.get(f'apis.{llm_source}.base_url')
    model_name = config_manager.get(f'apis.{llm_source}.model_name')

    return LLMAPIClient(api_key, model_name=model_name, base_url=base_url)



def case_reception_api_streaming(zip_file_path, output_dir):
    """
    报案接待处理流式API - 处理上传的zip文件

    Args:
        zip_file_path: 上传的zip文件路径
        output_dir: 输出目录

    Yields:
        tuple: (log_message, results) - results为None表示还在处理中，不为None表示处理完成
    """
    try:
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        yield "🚀 开始报案接待处理...\n", None

        # 1. 解压zip文件
        yield "📦 正在解压zip文件...\n", None
        temp_extract_dir = tempfile.mkdtemp(prefix="case_reception_")

        try:
            # 使用更好的方法处理中文文件名
            import locale
            system_encoding = locale.getpreferredencoding()

            with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                for member in zip_ref.infolist():
                    # 尝试修复中文文件名编码问题
                    try:
                        # 先尝试用cp437解码再用gbk编码（Windows zip常见问题）
                        if not member.filename.isascii():
                            try:
                                member.filename = member.filename.encode('cp437').decode('gbk')
                            except (UnicodeDecodeError, UnicodeEncodeError):
                                try:
                                    member.filename = member.filename.encode('cp437').decode('utf-8')
                                except (UnicodeDecodeError, UnicodeEncodeError):
                                    # 保持原文件名
                                    pass
                    except:
                        pass

                    # 提取文件
                    zip_ref.extract(member, temp_extract_dir)

            yield f"✅ zip文件解压完成，解压到: {temp_extract_dir}\n", None
        except Exception as e:
            yield f"❌ zip文件解压失败: {str(e)}\n", None
            return

        # 2. 图片OCR处理
        yield "\n🖼️ 开始图片OCR处理...\n", None
        for log_msg, ocr_result in _process_images_ocr_streaming(temp_extract_dir):
            yield log_msg, None

        if not ocr_result:
            yield "❌ 图片OCR处理失败\n", None
            return

        # 3. 字段提取处理
        yield "\n📋 开始字段提取处理...\n", None
        for log_msg, extraction_result in _process_field_extraction_streaming(temp_extract_dir, output_dir):
            yield log_msg, None
            if extraction_result is not None:
                # 处理完成，返回结果
                yield "🎉 报案接待处理完成！\n", extraction_result
                return

    except Exception as e:
        yield f'\n❌ 报案接待处理过程中发生错误：{str(e)}\n', None
        return
    finally:
        # 清理临时目录
        try:
            if 'temp_extract_dir' in locals():
                shutil.rmtree(temp_extract_dir)
        except:
            pass


def sie_api_streaming(input_file_list,output_file , config_info=''):
    """
    流式版本的结构化信息提取API

    Args:
        input_file_list: 输入文件列表
        config_info: 配置信息
        output_file: 输出文件路径

    Yields:
        tuple: (log_message, results) - results为None表示还在处理中，不为None表示处理完成
    """
    try:
        config = ConfigManager()
        # 确保输出目录存在
        os.makedirs(output_file, exist_ok=True)

        # 初始化LLM客户端
        llm_client = _create_default_llm_client()
        yield "🔧 初始化LLM客户端完成\n", None

    except Exception as e:
        yield f'\n❌ 处理过程中发生错误：{str(e)}\n', None
        return


def tdw_api_streaming(input_template, input_resources_list, output_file, original_title, new_title):
    """
    流式版本的模板驱动写作API

    Args:
        input_template: 模板文件路径
        input_resources_list: 参考资源文件路径列表
        output_file: 输出目录
        original_title: 原始标题
        new_title: 新标题

    Yields:
        tuple: (log_message, results) - results为None表示还在处理中，不为None表示处理完成
    """
    try:
        config = ConfigManager()
        # 确保输出目录存在
        os.makedirs(output_file, exist_ok=True)

        # 初始化LLM客户端
        llm_client = _create_default_llm_client()
        tdw_obj = TemplateDrivenWriting(temp_base_dir=output_file, llm_client=llm_client)

        yield "🔧 初始化LLM客户端完成\n", None

        tdw_obj.set_template(input_template)
        yield f"📄 加载模板文件: {os.path.basename(input_template)}\n", None

        for input_resources in input_resources_list:
            tdw_obj.add_resource(input_resources)
            yield f"📚 添加参考资源: {os.path.basename(input_resources)}\n", None

        # 第一步：切割模版文档
        yield "\n🔍 第一步：分析文章结构...\n", None
        summary = ''
        partitions = []
        retry_count = 0
        while (len(partitions) == 0 or summary == '') and retry_count < 3:  # 如果解析错误，重试一下
            retry_count = retry_count + 1
            yield f'📝 第{retry_count}次尝试解析文章结构...\n', None
            summary, partitions = tdw_obj.partitions()

        if len(partitions) == 0 or summary == '':
            yield '❌ 解析文章结构失败，请检查文章格式是否正确\n', None
            return

        yield f'✅ 文章摘要：{summary}\n', None
        yield f'✅ 文章结构解析成功，共{len(partitions)}个段落\n', None

        for i, partition in enumerate(partitions):
            yield f'   📋 段落{i+1}：{partition["summary"]}\n', None

        new_paragraphs = []
        # 第二~四步：每个段落串行处理
        yield f"\n🔄 开始处理各段落（共{len(partitions)}个）...\n", None

        for i, partition in enumerate(partitions):
            yield f'\n📝 开始处理第{i+1}个段落...\n', None

            if original_title != new_title:
                # 第二步： 生成写作指导
                retry_count = 0
                instruction = ''
                warnings = ''
                while instruction == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   🎯 第{retry_count}次尝试生成写作指导...\n', None
                    instruction, warnings = tdw_obj.object_replace(partition['slice'], original_title, new_title)

                if instruction == '':
                    yield f'   ❌ 段落{i+1}生成写作指导失败，跳过该段落\n', None
                    continue

                if isinstance(instruction, dict):
                    instruction = json.dumps(instruction, ensure_ascii=False, indent=2)
                if isinstance(warnings, dict):
                    warnings = json.dumps(warnings, ensure_ascii=False, indent=2)

                yield f'   ✅ 写作指导生成成功\n', None
                yield f'   📋 写作指导：{instruction}...\n', None
                yield f'   ⚠️  注意事项：{warnings}...\n', None

                # 第三步： 搜索参考内容
                yield f'   🔍 开始搜索参考内容...\n', None
                ref = []
                retry_count = 0
                while len(ref) == 0 and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   🔍 第{retry_count}次尝试搜索参考内容...\n', None
                    ref = tdw_obj.search(instruction)

                if len(ref) == 0:
                    yield f'   ❌ 段落{i+1}搜索参考内容失败，跳过该段落\n', None
                    continue

                yield f'   ✅ 搜索到{len(ref)}个相关片段\n', None

                # 将搜索到的段落列表转换为字符串，只包含段落内容
                ref_text = "'''" + "'''\n\n'''".join([item['text'] for item in ref if item['text'] is not None]) + "'''"

                # 第3.2步： 段落重写指导
                retry_count = 0
                rewrite_instruction = ''
                while rewrite_instruction == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   📝 第{retry_count}次尝试生成重写指导...\n', None
                    rewrite_instruction = tdw_obj.rewrite_instruction(partition['summary'], original_title, new_title, ref_text, instruction, warnings)

                if rewrite_instruction == '':
                    yield f'   ❌ 段落{i+1}重写写作指导失败，跳过该段落\n', None
                    continue

                if isinstance(rewrite_instruction, dict):
                    rewrite_instruction = json.dumps(rewrite_instruction, ensure_ascii=False, indent=2)

                yield f'   ✅ 重写指导生成成功\n', None

                # 第四步： 段落重写
                retry_count = 0
                new_paragraph = ''
                while new_paragraph == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    yield f'   ✏️  第{retry_count}次尝试段落重写...\n', None
                    new_paragraph = tdw_obj.rewrite(partition['slice'], ref_text, instruction)

                if new_paragraph == '':
                    yield f'   ❌ 段落{i+1}重写失败，跳过该段落\n', None
                    continue

                yield f'   ✅ 段落{i+1}重写成功\n', None
                yield f'   📄 重写后段落预览：{new_paragraph}...\n', None
                new_paragraphs.append({'seq_no': i, 'paragraph': new_paragraph})
            else:
                # 第三步： 搜索参考内容
                ref = []
                retry_count = 0
                while len(ref) == 0 and retry_count < 3:
                    retry_count = retry_count + 1
                    ref = tdw_obj.search(partition['summary'])
                if len(ref) == 0:
                    yield f'   ❌ 段落{i+1}搜索参考内容失败，跳过该段落\n', None
                    continue
                yield f'   ✅ 搜索到{len(ref)}个相关片段\n', None

                # 将搜索到的段落列表转换为字符串，只包含段落内容
                ref_text = "'''" + "'''\n\n'''".join([item['text'] for item in ref if item['text'] is not None]) + "'''"

                # 第四步： 段落重写
                retry_count = 0
                new_paragraph = ''
                while new_paragraph == '' and retry_count < 3:
                    retry_count = retry_count + 1
                    new_paragraph = tdw_obj.rewrite(partition['slice'], ref_text,
                                                    '根据素材对片段进行优化，补充缺失的内容，修复错误的信息')

                if new_paragraph == '':
                    yield f'   ❌ 段落{i+1}重写失败，跳过该段落\n', None
                    continue

                yield f'   ✅ 段落{i+1}重写成功\n', None
                yield f'   📄 重写后段落预览：{new_paragraph}...\n', None
                new_paragraphs.append({'seq_no': i, 'paragraph': new_paragraph})

        if len(new_paragraphs) == 0:
            yield '\n❌ 所有段落处理失败，无法生成文章\n', None
            return

        # 第五步：段落聚合
        yield f'\n📑 第五步：开始段落聚合（共{len(new_paragraphs)}个段落）...\n', None
        paragraphs_text = "\n\n".join([f"段落 {item['seq_no'] + 1}:\n{item['paragraph']}\n\n" for item in
                                       sorted(new_paragraphs, key=lambda x: x['seq_no'])
                                       if item['paragraph'] is not None])
        paragraphs_text_printout = "\n\n".join([f"{item['paragraph']}\n\n" for item in
                                                sorted(new_paragraphs, key=lambda x: x['seq_no'])
                                                if item['paragraph'] is not None])

        yield '✅ 段落聚合前准备完成\n', None

        new_article = tdw_obj.para_merge(paragraphs_text)
        yield '✅ 文章聚合成功\n', None

        # 第六步：文章润色
        yield '\n✨ 第六步：开始文章润色...\n', None
        polished = tdw_obj.polish(new_article)
        yield '✅ 文章润色成功\n', None

        yield '\n🎉 所有处理步骤完成！\n', None

        # 返回最终结果
        yield '✅ 处理完成，正在返回结果...\n', (str(paragraphs_text_printout), str(new_article), str(polished))

    except Exception as e:
        yield f'\n❌ 处理过程中发生错误：{str(e)}\n', None
        return


def multi_persona_writing_api_streaming(input_template, input_resources_list, output_file, original_title, new_title):
    """
    多人格写作决策流式API

    Args:
        input_template: 模板文件路径
        input_resources_list: 参考资源文件路径列表
        output_file: 输出目录
        original_title: 原始标题
        new_title: 新标题

    Yields:
        tuple: (log_message, results) - results为None表示还在处理中，不为None表示处理完成
    """
    try:
        config = ConfigManager()
        # 确保输出目录存在
        os.makedirs(output_file, exist_ok=True)

        # 初始化LLM客户端和多人格决策系统
        llm_client = _create_default_llm_client()
        mpd_system = MultiPersonaDecisionSystem(temp_base_dir=output_file, llm_client=llm_client)

        yield "🔧 初始化多人格决策系统完成\n", None

        # 设置模板和资源
        mpd_system.set_template(input_template)
        yield f"📄 加载模板文件: {os.path.basename(input_template)}\n", None

        for input_resources in input_resources_list:
            mpd_system.add_resource(input_resources)
            yield f"📚 添加参考资源: {os.path.basename(input_resources)}\n", None

        # 获取模板内容
        template_content = mpd_system.template.content
        reference_content = "\n\n".join([resource.content for resource in mpd_system.resources])

        yield "\n🧠 开始多人格协作分析...\n", None

        # 第一步：文档结构分析
        yield "📋 第一步：多人格文档结构分析...\n", None
        structure_decision = mpd_system.multi_persona_structure_analysis(template_content)

        yield f"✅ 结构分析完成，置信度: {structure_decision.confidence_level:.2f}\n", None
        yield f"📊 各人格意见汇总:\n", None

        persona_icons = {"暴躁老哥": "🔥", "自省姐": "🤔", "粉丝妹": "✨", "牛马小弟": "🐂"}
        for persona_response in structure_decision.persona_responses:
            icon = persona_icons.get(persona_response.persona_name, "🎭")
            yield f"   {icon} {persona_response.persona_name} (置信度: {persona_response.confidence_score:.2f}): {persona_response.response_content[:100]}...\n", None

        yield f"🎯 决策者最终决定: {structure_decision.final_decision[:200]}...\n", None

        # 第二步：内容重写决策
        yield "\n✏️ 第二步：多人格内容重写决策...\n", None
        writing_decision = mpd_system.multi_persona_writing_decision(
            template_content, original_title, new_title, reference_content
        )

        yield f"✅ 重写决策完成，置信度: {writing_decision.confidence_level:.2f}\n", None
        yield f"📊 各人格重写建议:\n", None

        for persona_response in writing_decision.persona_responses:
            icon = persona_icons.get(persona_response.persona_name, "🎭")
            yield f"   {icon} {persona_response.persona_name}: {persona_response.response_content[:150]}...\n", None
            if persona_response.suggestions:
                yield f"      💡 建议: {', '.join(persona_response.suggestions[:2])}...\n", None

        yield f"🎯 最终重写方案: {writing_decision.final_decision[:300]}...\n", None

        # 第三步：内容优化决策
        yield "\n✨ 第三步：多人格内容优化决策...\n", None
        optimization_goal = f"将内容从'{original_title}'主题转换为'{new_title}'主题，保持文章质量和可读性"
        optimization_decision = mpd_system.multi_persona_content_optimization(
            writing_decision.final_decision, optimization_goal, "保持原文风格和结构"
        )

        yield f"✅ 优化决策完成，置信度: {optimization_decision.confidence_level:.2f}\n", None
        yield f"🎯 最终优化方案: {optimization_decision.final_decision[:300]}...\n", None

        yield "\n🎉 多人格协作决策完成！\n", None

        # 整理最终结果
        final_results = {
            'structure_analysis': structure_decision.to_dict(),
            'writing_decision': writing_decision.to_dict(),
            'optimization_decision': optimization_decision.to_dict(),
            'summary': {
                'original_title': original_title,
                'new_title': new_title,
                'overall_confidence': (structure_decision.confidence_level +
                                     writing_decision.confidence_level +
                                     optimization_decision.confidence_level) / 3,
                'processing_timestamp': structure_decision.metadata.get('timestamp', '')
            }
        }

        # 返回最终结果
        yield '✅ 处理完成，正在返回结果...\n', final_results

    except Exception as e:
        yield f'\n❌ 多人格决策过程中发生错误：{str(e)}\n', None
        return


def _process_images_ocr_streaming(extract_dir):
    """
    处理解压目录中的所有图片文件，进行OCR转换

    Args:
        extract_dir: 解压后的目录路径

    Yields:
        tuple: (log_message, success_flag)
    """
    try:
        # 查找所有图片文件
        image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.gif', '*.bmp', '*.tiff']
        image_files = []

        for ext in image_extensions:
            pattern = os.path.join(extract_dir, "**", ext)
            image_files.extend(glob.glob(pattern, recursive=True))

        yield f"📊 找到 {len(image_files)} 个图片文件\n", None

        if len(image_files) == 0:
            yield "⚠️ 未找到图片文件\n", True
            return

        # 处理每个图片文件
        success_count = 0
        for i, img_path in enumerate(image_files, 1):
            try:
                # 使用正确的编码处理文件名
                img_basename = os.path.basename(img_path)
                try:
                    # 尝试正确显示中文文件名
                    display_name = img_basename.encode('utf-8').decode('utf-8')
                except:
                    # 如果编码有问题，使用原始文件名
                    display_name = img_basename

                yield f"🖼️ 正在处理 ({i}/{len(image_files)}): {display_name}\n", None

                # 构建输出路径 - 将图片扩展名改为.txt
                output_path = os.path.splitext(img_path)[0] + '.txt'

                # 使用OCR处理图片
                parse_img_document_by_paddleocr(img_path, output_path)

                # 输出文件名也需要正确编码
                output_basename = os.path.basename(output_path)
                try:
                    output_display_name = output_basename.encode('utf-8').decode('utf-8')
                except:
                    output_display_name = output_basename

                yield f"  ✅ OCR处理成功，保存到: {output_display_name}\n", None
                success_count += 1

            except Exception as ocr_error:
                yield f"  ❌ OCR处理失败: {str(ocr_error)}\n", None
                # 创建空文件标记处理失败
                try:
                    output_path = os.path.splitext(img_path)[0] + '.txt'
                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(f"OCR处理失败: {str(ocr_error)}")
                except:
                    pass

        yield f"📊 OCR处理完成，成功处理 {success_count}/{len(image_files)} 个文件\n", True

    except Exception as e:
        yield f"❌ 图片OCR处理过程中发生错误: {str(e)}\n", False


def _process_field_extraction_streaming(extract_dir, output_dir):
    """
    处理字段提取

    Args:
        extract_dir: 解压后的目录路径
        output_dir: 输出目录

    Yields:
        tuple: (log_message, results)
    """
    try:
        # 1. 加载配置
        yield "📋 加载字段提取配置...\n", None
        loader = SourceStructLoader()
        source_struct_dict = loader.load_config()

        # 更新基础文件夹路径为解压目录
        source_struct_dict.base_folder.folder_name = extract_dir

        # 2. 初始化LLM客户端
        try:
            config_manager = ConfigManager()
            api_config = config_manager.get('apis.glm')

            llm_client = LLMAPIClient(
                api_key=api_config['api_key'],
                model_name=api_config['model_name'],
                vision_model_name=api_config['vision_model_name'],
                base_url=api_config['base_url']
            )
            yield "✅ LLM客户端初始化成功\n", None
        except Exception as e:
            yield f"❌ LLM客户端初始化失败: {e}\n", None
            return

        # 3. 处理每个子文件夹
        all_results = {}
        base_folder_path = source_struct_dict.base_folder.folder_name

        for folder_key, folder_config in source_struct_dict.sub_folders.items():
            yield f"\n📁 处理子文件夹: {folder_key} ({folder_config.folder_name})\n", None
            yield f"📋 需要提取 {len(folder_config.field_list)} 个字段\n", None

            # 收集文件夹中的文本
            file_contents, combined_text = _collect_folder_texts(base_folder_path, folder_config.folder_name)

            if not combined_text:
                yield f"⚠️ 跳过空文件夹: {folder_config.folder_name}\n", None
                continue

            yield f"📝 合并文本长度: {len(combined_text)} 字符\n", None

            # 根据文件夹类型选择不同的处理方式
            if folder_key == 'liushui':
                # 银行流水使用表格提取方式
                yield f"🏦 使用银行流水表格提取模式\n", None

                # 处理每个txt文件及其对应的图片
                for i, (file_name, content) in enumerate(file_contents):
                    yield f"  📄 处理文件: {file_name}\n", None

                    # 构建txt文件的完整路径
                    txt_file_path = os.path.join(base_folder_path, folder_config.folder_name, file_name)

                    # 查找对应的图片文件
                    matching_images = _find_matching_image_files(txt_file_path)

                    if not matching_images:
                        yield f"  ⚠️ 未找到 {file_name} 对应的图片文件\n", None
                        continue

                    # 使用第一个匹配的图片
                    img_path = matching_images[0]
                    yield f"  🖼️ 使用图片: {os.path.basename(img_path)}\n", None

                    try:
                        # 图片文件转base64
                        with open(img_path, 'rb') as img_file:
                            img_base = base64.b64encode(img_file.read()).decode('utf-8')

                        result = _extract_liushui_table_with_llm(llm_client, content, folder_config.field_list, img_base)

                        if result['success']:
                            yield f"    ✅ 银行流水表格提取成功\n", None
                            result_data = result['result']
                            decision = result_data.get('decision', '未知')
                            row_count = result.get('row_count', 0)
                            yield f"    📊 提取结果: {decision} ({row_count} 行数据)\n", None
                        else:
                            yield f"    ❌ 银行流水表格提取失败: {result['error']}\n", None

                        # 保存结果（每个文件单独保存）
                        result_key = f"{folder_key}_{os.path.splitext(file_name)[0]}"
                        all_results[result_key] = {
                            'folder_name': folder_config.folder_name,
                            'file_name': file_name,
                            'image_file': os.path.basename(img_path),
                            'text_length': len(content),
                            'extraction_type': 'table_rows',
                            'table_result': result
                        }

                    except Exception as e:
                        yield f"    ❌ 处理文件 {file_name} 时出错: {e}\n", None

            else:
                # 使用单字段提取模式
                yield f"📝 使用单字段提取模式\n", None
                folder_results = {}

                for field_name in folder_config.field_list:
                    result = _extract_field_with_llm(llm_client, combined_text, field_name)
                    folder_results[field_name] = result

                    # 显示提取结果
                    if result['success']:
                        decision = result['result'].get('decision', '未知')
                        values_count = len(result['result'].get('values', []))
                        yield f"  ✅ {field_name}: {decision} ({values_count} 个值)\n", None
                    else:
                        yield f"  ❌ {field_name}: {result['error']}\n", None

                all_results[folder_key] = {
                    'folder_name': folder_config.folder_name,
                    'file_count': len(file_contents),
                    'text_length': len(combined_text),
                    'extraction_type': 'individual_fields',
                    'field_results': folder_results
                }

        # 4. 保存结果
        output_file = os.path.join(output_dir, 'extracted_results.json')
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)
            yield f"✅ 结果已保存到: {output_file}\n", None
        except Exception as e:
            yield f"❌ 保存结果失败: {e}\n", None

        # 返回最终结果
        yield "🎉 字段提取处理完成！\n", {
            'results_file': output_file,
            'results_data': all_results,
            'summary': {
                'total_folders': len(all_results),
                'output_file': output_file
            }
        }

    except Exception as e:
        yield f"❌ 字段提取处理过程中发生错误: {str(e)}\n", None


def _collect_folder_texts(base_folder_path, folder_name):
    """
    收集指定文件夹下的所有txt文件内容

    Args:
        base_folder_path: 基础文件夹路径
        folder_name: 子文件夹名称

    Returns:
        tuple: (文件列表, 合并的文本内容)
    """
    folder_path = os.path.join(base_folder_path, folder_name)

    if not os.path.exists(folder_path):
        return [], ""

    # 查找所有txt文件
    txt_files = glob.glob(os.path.join(folder_path, "*.txt"))

    if not txt_files:
        return [], ""

    # 按文件名排序以保持一致性
    txt_files.sort()

    file_contents = []
    combined_text = ""

    for txt_file in txt_files:
        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:  # 只处理非空文件
                    file_name = os.path.basename(txt_file)
                    file_contents.append((file_name, content))
                    combined_text += f"\n\n=== {file_name} ===\n{content}"
        except Exception as e:
            continue

    return file_contents, combined_text.strip()


def _find_matching_image_files(txt_file_path):
    """
    查找与txt文件同名的图片文件

    Args:
        txt_file_path: txt文件的完整路径

    Returns:
        list: 匹配的图片文件路径列表
    """
    # 支持的图片格式
    image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.tif', '.webp']

    # 获取txt文件的目录和基础文件名（不含扩展名）
    file_dir = os.path.dirname(txt_file_path)
    base_name = os.path.splitext(os.path.basename(txt_file_path))[0]

    matching_images = []
    for ext in image_extensions:
        image_path = os.path.join(file_dir, base_name + ext)
        if os.path.exists(image_path):
            matching_images.append(image_path)

    return matching_images


def _extract_field_with_llm(llm_client, text, field_name):
    """
    使用LLM提取单个字段
    """
    general_prompt = '''你是一个信息提取专家。你的任务是从给定的原始文本中查找是否包含指定目标字段的信息，并按照规定格式返回结果。

请从以下文本中查找目标字段：
"""
{source_data}
"""

目标字段为："{field_name}"

你需要完成的任务如下：
1. 判断原始文本中是否明确包含目标字段的信息。
2. 如果包含，请提取该字段的所有值，并标明它们在原文中的来源位置（如句子或段落内容）。每个值和其对应的来源位置应该配对。
3. 如果不包含，则对应字段留空（不要输出无关内容）。

请将结果以标准 JSON 格式返回，结构如下：
{{
  "decision": "是/否",
  "values": [
    {{
      "value": "字段的具体值（如果存在）",
      "source": "原始文本中对应的原文内容（如果存在）"
    }},
    ...
  ]
}}

注意：
- 输出必须为纯 JSON，不能包含其他解释性文字。
- 请尽量精准匹配字段内容，避免猜测或推断。
- 如果字段值有多个出现，请列出所有相关项，并确保每一项与其来源一一对应。
- 如果没有找到任何匹配项，"decision"应设为"否"，且"values"应为空列表([])。
'''

    try:
        prompt = general_prompt.format(source_data=text, field_name=field_name)
        result_text = llm_client.general_prompt(prompt)

        # 清理响应文本
        cleaned_text = result_text.strip()
        if cleaned_text.startswith('```json'):
            cleaned_text = cleaned_text[7:]
        if cleaned_text.endswith('```'):
            cleaned_text = cleaned_text[:-3]
        cleaned_text = cleaned_text.strip()

        # 尝试解析JSON结果
        try:
            result_json = json.loads(cleaned_text)

            # 验证结果结构
            if 'decision' in result_json and 'values' in result_json:
                return {
                    'success': True,
                    'result': result_json,
                    'raw_response': result_text
                }
            else:
                return {
                    'success': False,
                    'error': "结果结构不完整，缺少decision或values字段",
                    'raw_response': result_text,
                    'cleaned_text': cleaned_text
                }
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f"JSON解析失败: {str(e)}",
                'raw_response': result_text,
                'cleaned_text': cleaned_text
            }

    except Exception as e:
        return {
            'success': False,
            'error': f"LLM调用失败: {str(e)}"
        }


def _extract_liushui_table_with_llm(llm_client, text, field_names, img_base64):
    """
    使用LLM提取银行流水表格数据
    """
    liushui_prompt = '''你是一个信息提取专家。你的任务是从给定的图片和辅助OCR文本中识别并提取表格信息。

以下是图片通过OCR识别后转换的文本数据，与图片内容一致，作为辅助参考：
"""
{source_data}
"""

目标表头字段为："{field_names}"

你需要完成的任务如下：

1. 判断图片中是否包含表格结构：
   - 表格结构是指：具有多个列名（即表头）及其对应的多行数据。
   - 若存在，请继续提取所有匹配的数据行。
   - 若不存在，请将 `decision` 设为"否"，且 `values` 返回空列表 `[]`。

2. 如果表格存在，则按以下方式提取每一行数据：
   - 按照目标表头字段顺序逐行记录所提取到的数据，缺失字段使用空字符串 `""` 占位。
   - 同时记录该行在原表格中的来源行，用于填充 `source` 字段。

请将结果以标准 JSON 格式返回，结构如下：
{{
  "decision": "是/否",
  "field_names": [目标表头字段列表],
  "values": [
    {{
      "value": [字段1值, 字段2值, 字段3值, ...],
      "source": "该行数据在原表格中的来源文本"
    }},
    ...
  ]
}}

注意：
- 输出必须为纯 JSON，不能包含其他解释性文字。
- 每行的 `value` 数组长度必须与 `field_names` 数组长度一致。
- 如果某个字段在某行中缺失，请用空字符串 `""` 占位。
- 如果没有找到表格结构，"decision"应设为"否"，且"values"应为空列表([])。
'''

    try:
        prompt = liushui_prompt.format(source_data=text, field_names=', '.join(field_names))
        result_text = llm_client.vision_prompt(prompt, img_base64)

        # 清理响应文本
        cleaned_text = result_text.strip()
        if cleaned_text.startswith('```json'):
            cleaned_text = cleaned_text[7:]
        if cleaned_text.endswith('```'):
            cleaned_text = cleaned_text[:-3]
        cleaned_text = cleaned_text.strip()

        # 尝试解析JSON结果
        try:
            result_json = json.loads(cleaned_text)

            # 验证结果结构
            if 'decision' in result_json and 'values' in result_json:
                decision = result_json.get('decision', '否')
                values = result_json.get('values', [])

                return {
                    'success': True,
                    'result': result_json,
                    'raw_response': result_text,
                    'row_count': len(values)
                }
            else:
                return {
                    'success': False,
                    'error': "结果结构不完整，缺少decision或values字段",
                    'raw_response': result_text,
                    'cleaned_text': cleaned_text
                }
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f"JSON解析失败: {str(e)}",
                'raw_response': result_text,
                'cleaned_text': cleaned_text
            }

    except Exception as e:
        return {
            'success': False,
            'error': f"LLM调用失败: {str(e)}"
        }


if __name__ == '__main__':
    # 测试流式API
    input_template = 'asset/天津市债务融资工具总体情况.docx'
    input_resources_list = ['asset/北京市债务融资工具发展报告：历史演进、2024年现状与未来展望.docx']
    output_file = 'output'
    original_title = '天津市'
    new_title = '北京市'

    for log_msg, results in tdw_api_streaming(input_template, input_resources_list, output_file, original_title, new_title):
        print(log_msg, end='')
        if results is not None:
            print("处理完成！")
            break
