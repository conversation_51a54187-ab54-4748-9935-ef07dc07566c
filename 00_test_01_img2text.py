import os
import glob
from img_parser import img_ocr_parser_by_paddleocr, parse_img_document_by_paddleocr

def batch_process_images():
    """
    批量处理报案平台资料审核素材2文件夹中的所有图片文件
    将图片通过OCR转换为文本，保存到output文件夹的对应位置
    """
    # 源文件夹路径
    source_folder = "asset/报案平台资料审核素材2"
    # 目标文件夹路径
    output_folder = "asset/报案平台资料审核素材2"

    # 查找所有图片文件
    image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.gif', '*.bmp', '*.tiff']
    image_files = []

    for ext in image_extensions:
        pattern = os.path.join(source_folder, "**", ext)
        image_files.extend(glob.glob(pattern, recursive=True))

    print(f"找到 {len(image_files)} 个图片文件")

    # 处理每个图片文件
    for i, img_path in enumerate(image_files, 1):
        try:
            print(f"正在处理 ({i}/{len(image_files)}): {img_path}")

            # 构建输出路径
            # 将asset/报案平台资料审核素材2替换为output/报案平台资料审核素材2
            relative_path = os.path.relpath(img_path, source_folder)
            output_path = os.path.join(output_folder, relative_path)

            # 将文件扩展名改为.txt
            output_path = os.path.splitext(output_path)[0] + '.txt'

            # 创建输出目录
            output_dir = os.path.dirname(output_path)
            os.makedirs(output_dir, exist_ok=True)

            # 使用OCR处理图片并直接保存到文件
            try:
                parse_img_document_by_paddleocr(img_path, output_path)
                print(f"  ✓ 成功保存到: {output_path}")

            except Exception as ocr_error:
                print(f"  ✗ OCR处理失败: {str(ocr_error)}")
                # 创建空文件标记处理失败
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(f"OCR处理失败: {str(ocr_error)}")

        except Exception as e:
            print(f"  ✗ 处理文件失败: {str(e)}")
            continue

    print("批量处理完成！")

def list_image_files():
    """
    列出所有要处理的图片文件
    """
    source_folder = "asset/报案平台资料审核素材2"
    image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.gif', '*.bmp', '*.tiff']
    image_files = []

    for ext in image_extensions:
        pattern = os.path.join(source_folder, "**", ext)
        image_files.extend(glob.glob(pattern, recursive=True))

    print(f"找到 {len(image_files)} 个图片文件:")
    for i, img_path in enumerate(image_files, 1):
        print(f"{i:3d}. {img_path}")

    return image_files

if __name__ == "__main__":
    print("=== 图片批量转文本处理工具 ===")
    print("1. 列出所有图片文件")
    print("2. 开始批量处理")

    choice = input("请选择操作 (1/2): ").strip()

    if choice == "1":
        list_image_files()
    elif choice == "2":
        batch_process_images()
    else:
        print("无效选择，退出程序")