#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多人格决策系统测试脚本
"""

import os
import json
from multi_persona_decision import MultiPersonaDecisionSystem
from backend_apis_streaming import multi_persona_writing_api_streaming


def test_multi_persona_basic():
    """测试多人格决策系统基本功能"""
    print("🧪 开始测试多人格决策系统...")

    # 创建系统实例
    mpd_system = MultiPersonaDecisionSystem()

    # 测试简单决策
    task = "如何提高文章的可读性和吸引力？"
    context = "这是一篇关于金融市场分析的技术文章，目标读者是专业投资者。文章目前比较枯燥，数据很多但缺乏生动的案例。"

    print(f"📝 任务: {task}")
    print(f"📋 上下文: {context}")
    print("\n🎭 开始收集各人格意见...")
    print("   🔥 暴躁老哥：会犀利批评现有问题")
    print("   🤔 自省姐：会深度分析逻辑结构")
    print("   ✨ 粉丝妹：会发现文章亮点")
    print("   🐂 牛马小弟：会补全实施细节")

    try:
        decision_result = mpd_system.make_decision(task, context)

        print(f"\n📊 决策结果:")
        print(f"最终决策: {decision_result.final_decision}")
        print(f"置信度: {decision_result.confidence_level:.2f}")
        print(f"决策推理: {decision_result.decision_reasoning}")

        print(f"\n🎭 各人格意见:")
        persona_icons = {"暴躁老哥": "🔥", "自省姐": "🤔", "粉丝妹": "✨", "牛马小弟": "🐂"}
        for response in decision_result.persona_responses:
            icon = persona_icons.get(response.persona_name, "🎭")
            print(f"\n{icon} {response.persona_name} (置信度: {response.confidence_score:.2f}):")
            print(f"  意见: {response.response_content}")
            print(f"  推理: {response.reasoning}")
            if response.suggestions:
                print(f"  建议: {', '.join(response.suggestions)}")

        print("\n✅ 基本功能测试完成!")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_multi_persona_writing():
    """测试多人格写作决策功能"""
    print("\n🧪 开始测试多人格写作决策...")
    
    mpd_system = MultiPersonaDecisionSystem()
    
    # 模拟文章段落
    article_segment = """
    天津市作为重要的经济中心，其债务融资工具市场发展迅速。
    近年来，天津市政府积极推动金融创新，通过发行各类债券来支持基础设施建设和产业发展。
    数据显示，2023年天津市债务融资工具发行规模达到1500亿元，同比增长15%。
    """
    
    original_title = "天津市"
    new_title = "北京市"
    reference_materials = """
    北京市作为首都，拥有更加完善的金融市场体系。
    北京市债务融资工具市场规模更大，2023年发行规模超过3000亿元。
    北京市在绿色债券和创新型债券方面走在全国前列。
    """
    
    print(f"📝 原文段落: {article_segment.strip()}")
    print(f"🔄 主题转换: {original_title} → {new_title}")
    
    try:
        decision_result = mpd_system.multi_persona_writing_decision(
            article_segment, original_title, new_title, reference_materials
        )
        
        print(f"\n📊 写作决策结果:")
        print(f"最终决策: {decision_result.final_decision}")
        print(f"置信度: {decision_result.confidence_level:.2f}")
        
        print(f"\n🎭 各人格写作建议:")
        for response in decision_result.persona_responses:
            print(f"\n{response.persona_name}:")
            print(f"  建议: {response.response_content[:200]}...")
            if response.suggestions:
                print(f"  具体建议: {', '.join(response.suggestions[:3])}")
        
        print("\n✅ 写作决策测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 写作决策测试失败: {e}")
        return False


def test_streaming_api():
    """测试流式API（模拟）"""
    print("\n🧪 开始测试流式API...")
    
    # 注意：这里需要实际的文件路径，这只是演示
    # 在实际使用时，需要提供真实的文件路径
    print("📝 注意: 流式API测试需要实际的文件路径")
    print("可以使用以下方式调用:")
    print("""
    from backend_apis_streaming import multi_persona_writing_api_streaming
    
    # 示例调用
    for log_msg, results in multi_persona_writing_api_streaming(
        input_template='path/to/template.docx',
        input_resources_list=['path/to/resource1.docx'],
        output_file='output_dir',
        original_title='原主题',
        new_title='新主题'
    ):
        print(log_msg, end='')
        if results is not None:
            print("处理完成！")
            print(json.dumps(results, ensure_ascii=False, indent=2))
            break
    """)
    
    return True


def main():
    """主测试函数"""
    print("🚀 多人格决策系统测试开始")
    print("=" * 50)
    
    # 测试基本功能
    test1_result = test_multi_persona_basic()
    
    # 测试写作功能
    test2_result = test_multi_persona_writing()
    
    # 测试流式API
    test3_result = test_streaming_api()
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"基本功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"写作决策测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"流式API测试: {'✅ 通过' if test3_result else '❌ 失败'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 所有测试通过！多人格决策系统运行正常。")
    else:
        print("\n⚠️ 部分测试失败，请检查系统配置。")


if __name__ == "__main__":
    main()
