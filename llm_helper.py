import requests
import json
import time
import random

class LLMAPIClient:
    def __init__(self, api_key, model_name="glm-4-plus", vision_model_name='', base_url="https://open.bigmodel.cn/api/paas/v4/chat/completions"):
        """
        初始化bigmodel API客户端
        
        Args:
            api_key (str): 认证密钥
            base_url (str, optional): API基础URL
        """
        self.base_url = base_url
        self.headers = {
            'Accept': '*/*',
            'Authorization': api_key,
            'Content-Type': 'application/json'
        }
        self.default_params = {
            "model": model_name,
            "vision_model": vision_model_name,
            # "model": "Qwen/Qwen2-72B-Instruct-GPTQ-Int4",
            "stream": False,  # 默认关闭流式传输
            # "temperature": 0.7,
            # "extra_body": {"repetition_penalty": 1.2}
        }
    
    def chat(self, content, stream=False, temperature=None, max_retries=2, image_url=None):
        """
        发送聊天请求，支持重试机制
        
        Args:
            content (str): 用户消息内容
            stream (bool, optional): 是否使用流式传输
            temperature (float, optional): 控制随机性，覆盖默认值
            max_retries (int, optional): 最大重试次数，默认为2
        
        Returns:
            dict: API返回的响应
        
        Raises:
            Exception: 详细的API请求错误信息
        """
        # 可重试的错误类型
        retryable_exceptions = (
            requests.exceptions.ConnectionError, 
            requests.exceptions.Timeout, 
            requests.exceptions.HTTPError
        )
        
        for attempt in range(max_retries + 1):
            try:
                # 复制默认参数，避免修改原始配置
                params = self.default_params.copy()
                
                # 更新流式传输和温度参数
                params['stream'] = stream
                if temperature is not None:
                    params['temperature'] = temperature

                if image_url is not None:
                    content_array = [{
                        "type": "text", "text": content
                    }, {
                        "type": "image_url", "image_url": image_url
                    }]
                    params['model'] = params['vision_model']
                    # 构建消息体
                    params['messages'] = [
                        {"role": "user", "content": content_array}
                    ]
                else:
                    # 构建消息体
                    params['messages'] = [
                        {"role": "user", "content": content}
                    ]

                # 发送请求
                response = requests.post(
                    self.base_url, 
                    headers=self.headers, 
                    data=json.dumps(params)
                )
                
                # 如果响应状态码不是200，打印详细错误信息
                if response.status_code != 200:
                    print(f"API请求错误: 状态码 {response.status_code}")
                    print(f"响应内容: {response.text}")
                    response.raise_for_status()
                
                response_json = response.json()
                
                # 检查响应是否包含预期的字段
                if 'choices' not in response_json or not response_json['choices']:
                    raise ValueError(f"无效的API响应: {response_json}")
                
                return response_json
            
            except retryable_exceptions as e:
                # 最后一次尝试时抛出异常
                if attempt == max_retries:
                    print(f"API请求失败，已达到最大重试次数: {e}")
                    raise
                
                # 指数退避策略
                wait_time = (2 ** attempt) + random.random()
                print(f"API请求失败，第 {attempt + 1} 次重试。等待 {wait_time:.2f} 秒")
                time.sleep(wait_time)
                return None

            except Exception as e:
                # 对于非重试类型的异常，立即抛出
                print(f"API请求发生不可恢复的错误: {e}")
                raise
        return None

    def stream_chat(self, content):
        """
        流式传输聊天
        
        Args:
            content (str): 用户消息内容
        
        Yields:
            str: 流式返回的文本片段
        """
        # 复制默认参数
        params = self.default_params.copy()
        params['stream'] = True
        params['messages'] = [
            {"role": "user", "content": content}
        ]
        
        try:
            with requests.post(
                self.base_url, 
                headers=self.headers, 
                data=json.dumps(params), 
                stream=True
            ) as response:
                response.raise_for_status()
                for line in response.iter_lines():
                    if line:
                        # 解析每个流式返回的JSON行
                        chunk = json.loads(line.decode('utf-8'))
                        if 'choices' in chunk and chunk['choices']:
                            yield chunk['choices'][0]['delta'].get('content', '')
        except requests.RequestException as e:
            print(f"流式API请求错误: {e}")
