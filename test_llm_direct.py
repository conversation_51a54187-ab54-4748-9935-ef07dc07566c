#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试LLM API调用
"""

from llm_helper import LLMAPIClient
from config import ConfigManager
import base64
def test_llm_api():
    """测试LLM API直接调用"""
    print("🧪 测试LLM API直接调用")
    print("=" * 40)
    
    try:
        # 初始化LLM客户端
        config_manager = ConfigManager()
        api_config = config_manager.get('apis.glm')
        
        print(f"📋 API配置:")
        print(f"  Base URL: {api_config['base_url']}")
        print(f"  Model: {api_config['model_name']}")
        print(f"  API Key: {api_config['api_key'][:10]}...")
        
        llm_client = LLMAPIClient(
            api_key=api_config['api_key'],
            model_name=api_config['model_name'],
            base_url=api_config['base_url']
        )
        print("✅ LLM客户端初始化成功")
        
        # 测试简单的请求
#         test_prompt = """请回答一个简单的问题：1+1等于多少？
# 请用JSON格式回答：
# {
#   "answer": "你的答案",
#   "explanation": "简单解释"
# }"""
        test_prompt = """请描述你看到了什么"""

        print(f"\n📤 发送测试请求...")
        print(f"📝 请求内容: {test_prompt}")
        img_path = 'asset/报案平台资料审核素材2/报案平台资料审核素材/中联硅谷（北京）股权投资基金管理有限公司/报案书/截图20250609154247.png'
        with open(img_path, 'rb') as img_file:
            img_base = base64.b64encode(img_file.read()).decode('utf-8')

        response = llm_client.chat(test_prompt, img_base)
        
        print(f"\n📥 响应结果:")
        print(f"  类型: {type(response)}")
        print(f"  内容: {response}")
        
        if response and 'choices' in response:
            content = response['choices'][0]['message']['content']
            print(f"\n📝 响应文本:")
            print(f"  长度: {len(content)} 字符")
            print(f"  内容: {repr(content)}")
            print(f"  格式化内容:")
            print(content)
        else:
            print("❌ 响应格式异常")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_llm_api()
