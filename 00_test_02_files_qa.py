import os
import glob
import json
from source_struct_loader import SourceStructLoader, load_source_struct_config
from llm_helper import LLMAPIClient
from config import ConfigManager

general_prompt = '''你是一个信息提取专家。你的任务是从给定的原始文本中查找是否包含指定目标字段的信息，并按照规定格式返回结果。

请从以下文本中查找目标字段：
"""
{source_data}
"""

目标字段为："{field_name}"

你需要完成的任务如下：
1. 判断原始文本中是否明确包含目标字段的信息。
2. 如果包含，请提取该字段的所有值，并标明它们在原文中的来源位置（如句子或段落内容）。每个值和其对应的来源位置应该配对。
3. 如果不包含，则对应字段留空（不要输出无关内容）。

请将结果以标准 JSON 格式返回，结构如下：
{{
  "decision": "是/否",
  "values": [
    {{
      "value": "字段的具体值（如果存在）",
      "source": "原始文本中对应的原文内容（如果存在）"
    }},
    ...
  ]
}}

注意：
- 输出必须为纯 JSON，不能包含其他解释性文字。
- 请尽量精准匹配字段内容，避免猜测或推断。
- 如果字段值有多个出现，请列出所有相关项，并确保每一项与其来源一一对应。
- 如果没有找到任何匹配项，“decision”应设为“否”，且“values”应为空列表([])。

示例输出：
{{
  "decision": "是",
  "values": [
    {{
      "value": "第一个值",
      "source": "这是第一个值的来源文本。"
    }},
    {{
      "value": "第二个值",
      "source": "这是第二个值的来源文本。"
    }}
  ]
}}
'''


liushu_prompt = '''你是一个信息提取专家。你的任务是从给定的图片中识别是否存在目标表格结构，并提取其中的数据行信息。

以下是图片通过OCR识别后转换的文本数据，作为辅助参考：
"""
{source_data}
"""

目标表头字段为："{field_names}"

你需要完成的任务如下：

1. 判断图片中是否包含与目标表头字段匹配的表格结构：
   - 表格结构是指：具有多个列名（即表头）及其对应的多行数据。
   - 若存在，请继续提取所有匹配的数据行。
   - 若不存在，请将 `decision` 设为“否”，且 `values` 返回空列表 `[]`。

2. 如果表格存在，则按以下方式提取每一行数据：
   - 每个数据行应对应目标表头字段顺序，缺失字段使用空字符串 `""` 占位。
   - 同时记录该行在原文中的来源内容（如段落或句子），用于填充 `source` 字段。

3. 输出结果必须为标准 JSON 格式，结构如下：

{{
  "decision": "是/否",
  "field_names": ["字段1", "字段2", "字段3", ...],
  "values": [
    {{
      "value": ["字段1的值", "字段2的值", "..."],
      "source": "此行数据来源于原文的哪一部分"
    }},
    ...
  ]
}}

注意：
- 输出必须为纯 JSON，不能包含任何解释性文字。
- `field_names` 应严格按照传入参数的顺序排列。
- `value` 中每个字段值应与 `field_names` 对应位置保持一致，若某字段未找到则用空字符串 `""` 占位。
- 多行数据需按其在原文中出现的顺序返回。
- 提取内容必须严格来自原文，不得进行推断或编造。
- 如果没有找到任何匹配项，“decision”应设为“否”，且“values”应为空列表([])。

示例输出：
{{
  "decision": "是",
  "field_names": ["投资户名", "投资/返款账号", "投资/返款金额"],
  "values": [
    {{
      "value": ["张三", "111", "1000000"],
      "source": "这是第一行数据的来源文本。"
    }},
    {{
      "value": ["张三", "222", "3000000"],
      "source": "这是第二行数据的来源文本。"
    }}
  ]
}}
'''


def find_matching_image_files(txt_file_path):
    """
    查找与txt文件同名的图片文件

    Args:
        txt_file_path: txt文件的完整路径

    Returns:
        list: 匹配的图片文件路径列表
    """
    # 支持的图片格式
    image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.tif', '.webp']

    # 获取txt文件的目录和基础文件名（不含扩展名）
    file_dir = os.path.dirname(txt_file_path)
    base_name = os.path.splitext(os.path.basename(txt_file_path))[0]

    matching_images = []

    # 遍历所有支持的图片格式
    for ext in image_extensions:
        image_path = os.path.join(file_dir, base_name + ext)
        if os.path.exists(image_path):
            matching_images.append(image_path)

    return matching_images


def find_matching_image_files_by_name(file_name, search_directory=None):
    """
    根据文件名查找同名的图片文件（不需要完整路径）

    Args:
        file_name: 文件名（如 "document.txt"）
        search_directory: 搜索目录，如果为None则使用文件名所在目录

    Returns:
        list: 匹配的图片文件路径列表
    """
    # 支持的图片格式
    image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.tif', '.webp']

    # 获取基础文件名（不含扩展名）
    base_name = os.path.splitext(file_name)[0]

    # 如果没有指定搜索目录，使用当前目录
    if search_directory is None:
        search_directory = os.path.dirname(file_name) if os.path.dirname(file_name) else '.'

    matching_images = []

    # 遍历所有支持的图片格式
    for ext in image_extensions:
        image_path = os.path.join(search_directory, base_name + ext)
        if os.path.exists(image_path):
            matching_images.append(image_path)

    return matching_images


def collect_folder_texts(base_folder_path, folder_name):
    """
    收集指定文件夹下的所有txt文件内容

    Args:
        base_folder_path: 基础文件夹路径
        folder_name: 子文件夹名称

    Returns:
        tuple: (文件列表, 合并的文本内容)
    """
    folder_path = os.path.join(base_folder_path, folder_name)

    if not os.path.exists(folder_path):
        print(f"⚠️ 文件夹不存在: {folder_path}")
        return [], ""

    # 查找所有txt文件
    txt_files = glob.glob(os.path.join(folder_path, "*.txt"))

    if not txt_files:
        print(f"⚠️ 文件夹中没有txt文件: {folder_path}")
        return [], ""

    print(f"📁 找到 {len(txt_files)} 个txt文件在 {folder_name}")

    # 读取并合并所有文件内容
    combined_text = ""
    file_contents = []

    # 对文件列表按文件名排序
    txt_files.sort()  # 按文件名字母顺序排序

    # # 按文件名排序（如果文件名包含数字，可以使用自然排序）
    # 自然排序的好处是能正确处理像 file1.txt, file2.txt, file10.txt 这样的文件名，确保 file10.txt 排在 file2.txt 之后，而不是之前。
    # import re
    #
    # def natural_sort_key(filename):
    #     """自然排序键，正确处理文件名中的数字"""
    #     return [int(text) if text.isdigit() else text.lower() for text in re.split('([0-9]+)', filename)]
    #
    # txt_files.sort(key=natural_sort_key)  # 自然排序

    for txt_file in txt_files:
        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:  # 只添加非空内容
                    file_name = os.path.basename(txt_file)
                    file_contents.append((file_name, content))
                    combined_text += f"\n=== 文件: {file_name} ===\n{content}\n"
        except Exception as e:
            print(f"❌ 读取文件失败 {txt_file}: {e}")
            continue

    return file_contents, combined_text.strip()


def extract_field_with_llm(llm_client, combined_text, field_name):
    """
    使用LLM从文本中提取指定字段

    Args:
        llm_client: LLM客户端
        combined_text: 合并的文本内容
        field_name: 要提取的字段名称

    Returns:
        dict: 提取结果
    """
    try:
        # 使用general_prompt模板
        prompt = general_prompt.format(
            source_data=combined_text,
            field_name=field_name
        )

        print(f"🔍 正在提取字段: {field_name}")

        # 调用LLM
        print(f"  📤 发送请求到LLM...")
        response = llm_client.chat(prompt)

        # 调试信息：显示完整响应
        print(f"  📥 LLM响应类型: {type(response)}")
        print(f"  📥 LLM响应内容: {response}")

        if response and 'choices' in response:
            result_text = response['choices'][0]['message']['content'].strip()

            # 调试信息：显示原始响应
            print(f"  📝 原始响应长度: {len(result_text)} 字符")
            print(f"  📝 原始响应完整内容: {repr(result_text)}")

            if len(result_text) > 200:
                print(f"  📝 原始响应前200字符: {repr(result_text[:200])}")
                print(f"  📝 原始响应后200字符: {repr(result_text[-200:])}")

            # 尝试清理响应文本
            cleaned_text = result_text

            # 移除可能的markdown代码块标记
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.startswith('```'):
                cleaned_text = cleaned_text[3:]
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]

            # 移除前后空白
            cleaned_text = cleaned_text.strip()

            # 尝试解析JSON结果
            try:
                result_json = json.loads(cleaned_text)
                print(f"  ✅ JSON解析成功")
                return {
                    'field_name': field_name,
                    'success': True,
                    'result': result_json,
                    'raw_response': result_text
                }
            except json.JSONDecodeError as e:
                print(f"  ⚠️ JSON解析失败: {e}")
                print(f"  📝 清理后的文本: {repr(cleaned_text[:100])}")

                # 尝试修复常见的JSON格式问题
                try:
                    # 如果文本看起来像是被截断的JSON，尝试找到完整的JSON
                    if '{' in cleaned_text and '}' in cleaned_text:
                        start_idx = cleaned_text.find('{')
                        end_idx = cleaned_text.rfind('}') + 1
                        json_part = cleaned_text[start_idx:end_idx]

                        result_json = json.loads(json_part)
                        print(f"  ✅ JSON修复成功")
                        return {
                            'field_name': field_name,
                            'success': True,
                            'result': result_json,
                            'raw_response': result_text
                        }
                except:
                    pass

                return {
                    'field_name': field_name,
                    'success': False,
                    'error': f"JSON解析失败: {e}",
                    'raw_response': result_text,
                    'cleaned_text': cleaned_text
                }
        else:
            print(f"  ❌ LLM响应格式错误")
            return {
                'field_name': field_name,
                'success': False,
                'error': "LLM响应为空或格式错误",
                'raw_response': str(response)
            }

    except Exception as e:
        print(f"❌ 提取字段失败 {field_name}: {e}")
        print(f"  🔍 异常类型: {type(e)}")
        print(f"  🔍 异常详情: {repr(e)}")
        import traceback
        print(f"  🔍 异常堆栈: {traceback.format_exc()}")
        return {
            'field_name': field_name,
            'success': False,
            'error': str(e),
            'raw_response': None
        }


def extract_liushui_table_with_llm(llm_client, combined_text, field_list, img_url):
    """
    使用LLM从银行流水文本中提取表格数据（按行提取）

    Args:
        llm_client: LLM客户端
        combined_text: 合并的文本内容
        field_list: 字段列表

    Returns:
        dict: 提取结果
    """
    try:
        # 将字段列表转换为字符串
        field_names = '", "'.join(field_list)
        field_names = f'"{field_names}"'

        # 使用liushui_prompt模板
        prompt = liushu_prompt.format(
            source_data=combined_text,
            field_names=field_names
        )

        print(f"🔍 正在提取银行流水表格数据")
        print(f"📋 字段列表: {field_list}")

        # 调用LLM
        print(f"  📤 发送请求到LLM...")
        response = llm_client.chat(prompt, img_url)

        # 调试信息：显示完整响应
        print(f"  📥 LLM响应类型: {type(response)}")

        if response and 'choices' in response:
            result_text = response['choices'][0]['message']['content'].strip()

            # 调试信息：显示原始响应
            print(f"  📝 原始响应长度: {len(result_text)} 字符")
            if len(result_text) < 500:
                print(f"  📝 原始响应内容: {repr(result_text)}")
            else:
                print(f"  📝 原始响应前200字符: {repr(result_text[:200])}")
                print(f"  📝 原始响应后200字符: {repr(result_text[-200:])}")

            # 尝试清理响应文本
            cleaned_text = result_text

            # 移除可能的markdown代码块标记
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.startswith('```'):
                cleaned_text = cleaned_text[3:]
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]

            # 移除前后空白
            cleaned_text = cleaned_text.strip()

            # 尝试解析JSON结果
            try:
                result_json = json.loads(cleaned_text)
                print(f"  ✅ JSON解析成功")

                # 验证结果结构
                if 'decision' in result_json and 'values' in result_json:
                    decision = result_json.get('decision', '否')
                    values = result_json.get('values', [])

                    print(f"  📊 提取结果: {decision}")
                    print(f"  📊 数据行数: {len(values)}")

                    return {
                        'success': True,
                        'result': result_json,
                        'raw_response': result_text,
                        'row_count': len(values)
                    }
                else:
                    print(f"  ⚠️ 结果结构不完整")
                    return {
                        'success': False,
                        'error': "结果结构不完整，缺少decision或values字段",
                        'raw_response': result_text,
                        'cleaned_text': cleaned_text
                    }

            except json.JSONDecodeError as e:
                print(f"  ⚠️ JSON解析失败: {e}")
                print(f"  📝 清理后的文本: {repr(cleaned_text[:200])}")

                # 尝试修复常见的JSON格式问题
                try:
                    # 如果文本看起来像是被截断的JSON，尝试找到完整的JSON
                    if '{' in cleaned_text and '}' in cleaned_text:
                        start_idx = cleaned_text.find('{')
                        end_idx = cleaned_text.rfind('}') + 1
                        json_part = cleaned_text[start_idx:end_idx]

                        result_json = json.loads(json_part)
                        print(f"  ✅ JSON修复成功")

                        values = result_json.get('values', [])
                        return {
                            'success': True,
                            'result': result_json,
                            'raw_response': result_text,
                            'row_count': len(values)
                        }
                except:
                    pass

                return {
                    'success': False,
                    'error': f"JSON解析失败: {e}",
                    'raw_response': result_text,
                    'cleaned_text': cleaned_text
                }
        else:
            print(f"  ❌ LLM响应格式错误")
            return {
                'success': False,
                'error': "LLM响应为空或格式错误",
                'raw_response': str(response)
            }

    except Exception as e:
        print(f"❌ 提取银行流水表格失败: {e}")
        print(f"  🔍 异常类型: {type(e)}")
        print(f"  🔍 异常详情: {repr(e)}")
        import traceback
        print(f"  🔍 异常堆栈: {traceback.format_exc()}")
        return {
            'success': False,
            'error': str(e),
            'raw_response': None
        }


def test_single_field_extraction():
    """
    测试单个字段提取，用于调试
    """
    print("🧪 测试单个字段提取")
    print("=" * 40)

    # 1. 加载配置
    loader = SourceStructLoader()
    source_struct_dict = loader.load_config()

    # 2. 初始化LLM客户端
    try:
        config_manager = ConfigManager()
        api_config = config_manager.get('apis.glm')

        llm_client = LLMAPIClient(
            api_key=api_config['api_key'],
            model_name=api_config['model_name'],
            vision_model_name=api_config['vision_model_name'],
            base_url=api_config['base_url']
        )
        print("✅ LLM客户端初始化成功")
    except Exception as e:
        print(f"❌ LLM客户端初始化失败: {e}")
        return

    # 3. 选择第一个子文件夹进行测试
    first_folder_key = list(source_struct_dict.sub_folders.keys())[0]
    folder_config = source_struct_dict.sub_folders[first_folder_key]

    print(f"📁 测试文件夹: {first_folder_key} ({folder_config.folder_name})")

    # 4. 收集文本
    base_folder_path = source_struct_dict.base_folder.folder_name
    _, file_contents, combined_text = collect_folder_texts(base_folder_path, folder_config.folder_name)

    if not combined_text:
        print("❌ 没有找到文本内容")
        return

    print(f"📝 合并文本长度: {len(combined_text)} 字符")
    print(f"📝 文本预览: {combined_text[:200]}...")

    # 5. 测试第一个字段
    test_field = folder_config.field_list[0]
    print(f"\n🎯 测试字段: {test_field}")

    result = extract_field_with_llm(llm_client, combined_text, test_field)

    print(f"\n📊 测试结果:")
    print(f"  成功: {result['success']}")
    if result['success']:
        print(f"  结果: {result['result']}")
    else:
        print(f"  错误: {result['error']}")
        if 'cleaned_text' in result:
            print(f"  清理后文本: {result['cleaned_text'][:100]}...")


def process_subfolder_extraction(base_folder_path: str=None):
    """
    处理子文件夹的字段提取
    """
    print("🚀 开始子文件夹字段提取处理")
    print("=" * 60)

    # 1. 加载配置
    loader = SourceStructLoader()
    source_struct_dict = loader.load_config()

    if base_folder_path is not None:
        source_struct_dict.base_folder.folder_name = base_folder_path

    # 2. 初始化LLM客户端
    try:
        config_manager = ConfigManager()
        api_config = config_manager.get('apis.glm')  # 使用GLM配置

        llm_client = LLMAPIClient(
            api_key=api_config['api_key'],
            model_name=api_config['model_name'],
            vision_model_name=api_config['vision_model_name'],
            base_url=api_config['base_url']
        )
        print("✅ LLM客户端初始化成功")
    except Exception as e:
        print(f"❌ LLM客户端初始化失败: {e}")
        return

    # 3. 获取基础文件夹路径
    base_folder_path = source_struct_dict.base_folder.folder_name
    print(f"📂 基础文件夹: {base_folder_path}")

    # 4. 处理每个子文件夹
    all_results = {}

    for folder_key, folder_config in source_struct_dict.sub_folders.items():
        print(f"\n📁 处理子文件夹: {folder_key} ({folder_config.folder_name})")
        print(f"📋 需要提取 {len(folder_config.field_list)} 个字段")

        # 收集文件夹中的文本
        file_list, file_contents, combined_text = collect_folder_texts(base_folder_path, folder_config.folder_name)

        if not combined_text:
            print(f"⚠️ 跳过空文件夹: {folder_config.folder_name}")
            continue

        print(f"📝 合并文本长度: {len(combined_text)} 字符")

        # 根据文件夹类型选择不同的处理方式
        if folder_key == 'liushui':
            # 银行流水使用表格提取方式
            print(f"🏦 使用银行流水表格提取模式")
            for file_name in file_list:
                img_url = file_name[:-4] + '.png'
            result = extract_liushui_table_with_llm(llm_client, combined_text, folder_config.field_list, img_url)

            # 显示提取结果
            if result['success']:
                decision = result['result'].get('decision', '未知')
                row_count = result.get('row_count', 0)
                print(f"  ✅ 银行流水表格提取: {decision} ({row_count} 行数据)")
            else:
                print(f"  ❌ 银行流水表格提取失败: {result['error']}")

            # 保存结果（特殊格式）
            all_results[folder_key] = {
                'folder_name': folder_config.folder_name,
                'file_count': len(file_contents),
                'text_length': len(combined_text),
                'extraction_type': 'table_rows',
                'table_result': result
            }
        else:
            # 其他文件夹使用单字段提取方式
            print(f"📋 使用单字段提取模式")
            folder_results = {}

            for field_name in folder_config.field_list:
                result = extract_field_with_llm(llm_client, combined_text, field_name)
                folder_results[field_name] = result

                # 显示提取结果
                if result['success']:
                    decision = result['result'].get('decision', '未知')
                    values_count = len(result['result'].get('values', []))
                    print(f"  ✅ {field_name}: {decision} ({values_count} 个值)")
                else:
                    print(f"  ❌ {field_name}: {result['error']}")

            all_results[folder_key] = {
                'folder_name': folder_config.folder_name,
                'file_count': len(file_contents),
                'text_length': len(combined_text),
                'extraction_type': 'individual_fields',
                'field_results': folder_results
            }

    return all_results


def save_extraction_results(results, output_file="extraction_results.json"):
    """
    保存提取结果到文件

    Args:
        results: 提取结果字典
        output_file: 输出文件路径
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"✅ 结果已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")


def print_extraction_summary(results):
    """
    打印提取结果摘要

    Args:
        results: 提取结果字典
    """
    print("\n" + "=" * 60)
    print("📊 字段提取结果摘要")
    print("=" * 60)

    total_folders = len(results)
    total_fields = 0
    successful_extractions = 0

    for folder_key, folder_data in results.items():
        folder_name = folder_data['folder_name']
        extraction_type = folder_data.get('extraction_type', 'individual_fields')

        print(f"\n📁 {folder_key} ({folder_name})")
        print(f"   📄 文件数量: {folder_data['file_count']}")
        print(f"   📝 文本长度: {folder_data['text_length']} 字符")
        print(f"   🔧 提取模式: {extraction_type}")

        if extraction_type == 'table_rows':
            # 银行流水表格提取结果
            table_result = folder_data.get('table_result', {})
            if table_result.get('success', False):
                successful_extractions += 1
                total_fields += 1

                result_data = table_result.get('result', {})
                decision = result_data.get('decision', '未知')
                row_count = table_result.get('row_count', 0)

                print(f"   ✅ 表格提取: {decision} ({row_count} 行数据)")

                # 显示字段信息
                field_names = result_data.get('field_names', [])
                print(f"   📋 字段数量: {len(field_names)}")
                print(f"   📋 字段列表: {', '.join(field_names)}")

                folder_success = 1
                field_count = 1
            else:
                total_fields += 1
                error = table_result.get('error', '未知错误')
                print(f"   ❌ 表格提取失败: {error}")
                folder_success = 0
                field_count = 1
        else:
            # 单字段提取结果
            field_results = folder_data.get('field_results', {})
            print(f"   📋 字段数量: {len(field_results)}")

            folder_success = 0
            for field_name, result in field_results.items():
                total_fields += 1
                if result['success']:
                    successful_extractions += 1
                    folder_success += 1
                    decision = result['result'].get('decision', '未知')
                    values_count = len(result['result'].get('values', []))
                    print(f"      ✅ {field_name}: {decision} ({values_count} 个值)")
                else:
                    print(f"      ❌ {field_name}: {result['error']}")

            field_count = len(field_results)

        success_rate = (folder_success / field_count) * 100 if field_count > 0 else 0
        print(f"   📈 成功率: {success_rate:.1f}% ({folder_success}/{field_count})")

    overall_success_rate = (successful_extractions / total_fields) * 100 if total_fields else 0
    print(f"\n🎯 总体统计:")
    print(f"   📂 处理文件夹: {total_folders}")
    print(f"   📋 总字段数: {total_fields}")
    print(f"   ✅ 成功提取: {successful_extractions}")
    print(f"   📈 总成功率: {overall_success_rate:.1f}%")


def test_liushui_extraction():
    """
    测试银行流水表格提取，用于调试
    """
    print("🧪 测试银行流水表格提取")
    print("=" * 40)

    # 1. 加载配置
    loader = SourceStructLoader()
    source_struct_dict = loader.load_config()

    # 2. 初始化LLM客户端
    try:
        config_manager = ConfigManager()
        api_config = config_manager.get('apis.glm')

        llm_client = LLMAPIClient(
            api_key=api_config['api_key'],
            model_name=api_config['model_name'],
            vision_model_name=api_config['vision_model_name'],
            base_url=api_config['base_url']
        )
        print("✅ LLM客户端初始化成功")
    except Exception as e:
        print(f"❌ LLM客户端初始化失败: {e}")
        return

    # 3. 获取liushui文件夹配置
    liushui_config = source_struct_dict.sub_folders.get('liushui')
    if not liushui_config:
        print("❌ 未找到liushui文件夹配置")
        return

    print(f"📁 测试文件夹: liushui ({liushui_config.folder_name})")
    print(f"📋 字段列表: {liushui_config.field_list}")

    # 4. 收集文本
    base_folder_path = source_struct_dict.base_folder.folder_name
    file_list, file_contents, combined_text = collect_folder_texts(base_folder_path, liushui_config.folder_name)

    if not combined_text:
        print("❌ 没有找到文本内容")
        return

    print(f"📝 合并文本长度: {len(combined_text)} 字符")
    print(f"📝 文本预览: {combined_text[:300]}...")

    # 5. 测试表格提取
    print(f"\n🎯 开始银行流水表格提取")

    result = extract_liushui_table_with_llm(llm_client, combined_text, liushui_config.field_list)

    print(f"\n📊 测试结果:")
    print(f"  成功: {result['success']}")
    if result['success']:
        result_data = result['result']
        decision = result_data.get('decision', '未知')
        values = result_data.get('values', [])
        field_names = result_data.get('field_names', [])

        print(f"  决策: {decision}")
        print(f"  字段数: {len(field_names)}")
        print(f"  数据行数: {len(values)}")
        print(f"  字段列表: {field_names}")

        # 显示前几行数据
        if values:
            print(f"\n📋 前3行数据预览:")
            for i, row in enumerate(values[:3], 1):
                print(f"    第{i}行:")
                row_values = row.get('value', [])
                source_text = row.get('source', '')

                for j, field_name in enumerate(field_names):
                    field_value = row_values[j] if j < len(row_values) else 'null'
                    print(f"      {field_name}: {field_value}")

                print(f"      来源: {source_text[:100]}...")
                print()
    else:
        print(f"  错误: {result['error']}")
        if 'cleaned_text' in result:
            print(f"  清理后文本: {result['cleaned_text'][:200]}...")


if __name__ == "__main__":
    print("🚀 字段提取处理程序")
    print("=" * 60)
    print("1. 运行字段提取")
    print("2. 测试单个字段提取（调试用）")
    print("3. 测试银行流水表格提取（调试用）")

    choice = input("请选择操作 (1/2/3): ").strip()

    prefix = 'asset/报案平台资料审核素材2/报案平台资料审核素材/'
    if choice == "1":
        folder_list = [prefix + '中联硅谷（北京）股权投资基金管理有限公司',
                       prefix + '安晓博等人（和信系）1',
                       prefix + '安晓博等人（和信系）2',
                       prefix + '安晓博等人（和信系）3',
                       prefix + '广信联合（北京）商务顾问有限公司(广信贷）1',
                       prefix + '广信联合（北京）商务顾问有限公司(广信贷）2'
                       ]
        for folder in folder_list:
            # 运行字段提取
            results = process_subfolder_extraction(folder)

            if results:
                # 打印摘要
                print_extraction_summary(results)

                # 保存结果
                save_extraction_results(results,output_file=folder+'/extracted_results.json')

                print("\n✅ 字段提取处理完成！")
            else:
                print("❌ 字段提取处理失败")

    elif choice == "2":
        # 运行单个字段测试
        test_single_field_extraction()
    elif choice == "3":
        # 运行银行流水表格提取测试
        test_liushui_extraction()
    else:
        print("无效选择，退出程序")

