#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FinPOC - 金融行业POC验证平台
基于Gradio的Web界面
"""

import gradio as gr
import os
import tempfile
import shutil
from datetime import datetime
import threading
import queue
import sys
from io import StringIO
from contextlib import redirect_stdout, redirect_stderr
from backend_apis_streaming import tdw_api_streaming, case_reception_api_streaming


class LogCapture:
    """捕获print输出的类"""
    def __init__(self):
        self.log_queue = queue.Queue()
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr

    def start_capture(self):
        """开始捕获输出"""
        sys.stdout = self
        sys.stderr = self

    def stop_capture(self):
        """停止捕获输出"""
        sys.stdout = self.original_stdout
        sys.stderr = self.original_stderr

    def write(self, text):
        """重写write方法来捕获输出"""
        if text.strip():  # 只记录非空内容
            self.log_queue.put(text)
        self.original_stdout.write(text)  # 同时输出到原始stdout

    def flush(self):
        """刷新缓冲区"""
        self.original_stdout.flush()

    def get_logs(self):
        """获取所有日志"""
        logs = []
        while not self.log_queue.empty():
            try:
                logs.append(self.log_queue.get_nowait())
            except queue.Empty:
                break
        return ''.join(logs)


# 全局变量存储用户偏好
user_preferences = {
    'preferred_result': None,
    'feedback_count': {'paragraphs': 0, 'article': 0, 'polished': 0}
}

# 全局日志捕获器
log_capture = LogCapture()


def create_homepage():
    """创建主页内容"""
    homepage_content = """
    ## 欢迎来到金融行业POC验证平台

    **我们是FDE（Forward Deployed Engineer）** - 前沿部署工程师，我们的核心理念是：

    ### 🎯 我们的使命
    - **不坐在办公室调模型**，而是坐在客户身边
    - **听懂客户的真实需求**，理解他们如何描述问题
    - **当场编写提示词**，立即展示解决方案
    - **用实际结果说话**，让客户看到效果

    ### 💡 核心特征

    #### 🔧 技术与业务并重
    - 技术不一定最强，但**能听懂业务**
    - 深入理解客户痛点和实际需求
    - 将技术能力转化为业务价值

    #### ⚡ 敏捷响应
    - **不写长篇需求文档**，直接调试提示词
    - 快速构建Demo，立即验证想法
    - 迭代优化，持续改进

    #### 🚀 先行者精神
    - **不等团队产品化**，带着模型"先干起来"
    - 在一线探索最佳实践
    - 为产品化积累宝贵经验

    ---

    ### 🛠️ 平台功能

    - **📝 模版化写作**：基于模板和素材智能生成高质量文档
    - **👍 偏好标注**：收集用户反馈，持续优化模型效果
    - **📊 效果分析**：跟踪使用情况，分析改进方向

    ---

    *让我们一起成为客户身边的技术伙伴，用AI的力量解决实际问题！*
    """

    return gr.Markdown(homepage_content)


def process_template_writing(original_title, new_title, template_file, resource_files, progress=gr.Progress()):
    """处理模版化写作请求 - 支持实时日志更新"""
    try:
        # 参数验证
        if not original_title or not new_title:
            yield "❌ 请填写原标题和新标题", "", "", ""
            return

        if not template_file:
            yield "❌ 请上传模板文件", "", "", ""
            return

        if not resource_files:
            yield "❌ 请上传至少一个参考资源文件", "", "", ""
            return

        # 确保resource_files是列表
        if not isinstance(resource_files, list):
            resource_files = [resource_files] if resource_files else []

        progress(0.1, desc="准备文件...")
        yield "🚀 开始处理模版化写作任务...\n", "", "", ""

        # 创建临时输出目录
        output_dir = tempfile.mkdtemp(prefix="tdw_output_")

        # 获取文件路径
        template_path = template_file.name
        resource_paths = [f.name for f in resource_files]

        yield f"📁 模板文件: {template_path}\n📚 参考资源: {', '.join([os.path.basename(p) for p in resource_paths])}\n\n", "", "", ""

        progress(0.2, desc="开始处理...")

        # 调用后端API - 使用生成器版本
        try:
            accumulated_logs = "🚀 开始处理模版化写作任务...\n"
            accumulated_logs += f"📁 模板文件: {template_path}\n📚 参考资源: {', '.join([os.path.basename(p) for p in resource_paths])}\n\n"

            # 调用流式处理版本的tdw_api
            for log_update, results in tdw_api_streaming(
                input_template=template_path,
                input_resources_list=resource_paths,
                output_file=output_dir,
                original_title=original_title,
                new_title=new_title
            ):
                accumulated_logs += log_update

                if results is None:
                    # 只是日志更新，还没有最终结果
                    yield accumulated_logs, "", "", ""
                else:
                    # 有最终结果了
                    paragraphs_text_printout, new_article, polished = results
                    progress(1.0, desc="✅ 处理完成！")
                    yield accumulated_logs, paragraphs_text_printout, new_article, polished
                    return

        except Exception as e:
            accumulated_logs += f"\n❌ 处理过程中出现错误：{str(e)}"
            yield accumulated_logs, "", "", ""
            return

        finally:
            # 清理临时目录
            try:
                shutil.rmtree(output_dir)
            except:
                pass

    except Exception as e:
        yield f"❌ 系统错误：{str(e)}", "", "", ""


def record_preference(choice):
    """记录用户偏好"""
    global user_preferences

    choice_map = {
        "段落文本": "paragraphs",
        "聚合文章": "article",
        "润色文章": "polished"
    }

    if choice in choice_map:
        key = choice_map[choice]
        user_preferences['feedback_count'][key] += 1
        user_preferences['preferred_result'] = choice

        # 记录时间戳
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return f"✅ 已记录您的偏好：{choice} (时间：{timestamp})\n当前统计：段落文本({user_preferences['feedback_count']['paragraphs']}) | 聚合文章({user_preferences['feedback_count']['article']}) | 润色文章({user_preferences['feedback_count']['polished']})"
    else:
        return "❌ 无效的选择"


def create_template_writing_interface():
    """创建模版化写作界面"""
    with gr.Column():
        gr.Markdown("# 📝 模版化写作")
        gr.Markdown("上传模板文件和参考资源，系统将基于模板结构和参考内容生成新的文章。")

        with gr.Row():
            with gr.Column(scale=1):
                original_title = gr.Textbox(
                    label="原标题",
                    placeholder="请输入原始标题（如：恒丰银行2024年年报的热点评述）",
                    info="模板中需要替换的原始标题"
                )
                new_title = gr.Textbox(
                    label="新标题",
                    placeholder="请输入新标题（如：雅安市商业银行2024年年报的热点评述）",
                    info="要替换成的新标题"
                )

            with gr.Column(scale=1):
                template_file = gr.File(
                    label="模板文件",
                    file_types=[".docx", ".pdf"],
                    file_count="single",
                    # info="上传单个模板文件（支持.docx和.pdf格式）"
                )
                resource_files = gr.File(
                    label="参考资源文件",
                    file_types=[".docx", ".pdf"],
                    file_count="multiple",
                    value=None,
                    # info="上传多个参考资源文件（支持.docx和.pdf格式）"
                )

        # 处理按钮
        process_btn = gr.Button("🚀 开始处理", variant="primary", size="lg")

        # 日志显示区域
        gr.Markdown("## 📋 处理日志")
        log_output = gr.Textbox(
            label="实时日志",
            lines=10,
            max_lines=20,
            show_copy_button=True,
            info="显示处理过程中的详细日志信息"
        )

        # 结果展示区域
        gr.Markdown("## 📄 生成结果")
        gr.Markdown("以下是三种不同处理阶段的结果，请选择您最满意的版本：")

        with gr.Row():
            with gr.Column():
                gr.Markdown("### 📝 重写文章")
                with gr.Group():
                    paragraphs_output = gr.Markdown(
                        value="*等待处理结果...*",
                        elem_classes=["result-markdown"]
                    )
                paragraphs_btn = gr.Button("👍 选择重写文章", variant="secondary")

            with gr.Column():
                gr.Markdown("### 📰 微调文章")
                with gr.Group():
                    article_output = gr.Markdown(
                        value="*等待处理结果...*",
                        elem_classes=["result-markdown"]
                    )
                article_btn = gr.Button("👍 选择微调文章", variant="secondary")

            with gr.Column():
                gr.Markdown("### ✨ 润色文章")
                with gr.Group():
                    polished_output = gr.Markdown(
                        value="*等待处理结果...*",
                        elem_classes=["result-markdown"]
                    )
                polished_btn = gr.Button("👍 选择润色文章", variant="secondary")

        # 偏好反馈显示
        preference_output = gr.Textbox(
            label="偏好反馈",
            lines=2,
            info="显示您的选择和当前统计信息"
        )

        # 绑定事件 - 支持流式更新
        process_btn.click(
            fn=process_template_writing,
            inputs=[original_title, new_title, template_file, resource_files],
            outputs=[log_output, paragraphs_output, article_output, polished_output],
            show_progress=True
        )

        paragraphs_btn.click(
            fn=lambda: record_preference("段落文本"),
            outputs=preference_output
        )

        article_btn.click(
            fn=lambda: record_preference("聚合文章"),
            outputs=preference_output
        )

        polished_btn.click(
            fn=lambda: record_preference("润色文章"),
            outputs=preference_output
        )


def create_preference_annotation_interface():
    """创建偏好标注界面"""
    with gr.Column():
        gr.Markdown("# 👍 偏好标注")
        gr.Markdown("这里将展示用户偏好标注功能，用于收集反馈和优化模型效果。")

        # 显示当前统计
        with gr.Row():
            with gr.Column():
                gr.Markdown("## 📊 当前偏好统计")
                stats_display = gr.Textbox(
                    label="偏好统计",
                    value=f"段落文本：{user_preferences['feedback_count']['paragraphs']} 次\n聚合文章：{user_preferences['feedback_count']['article']} 次\n润色文章：{user_preferences['feedback_count']['polished']} 次",
                    lines=3,
                    interactive=False
                )

                refresh_btn = gr.Button("🔄 刷新统计", variant="secondary")

                def refresh_stats():
                    return f"段落文本：{user_preferences['feedback_count']['paragraphs']} 次\n聚合文章：{user_preferences['feedback_count']['article']} 次\n润色文章：{user_preferences['feedback_count']['polished']} 次"

                refresh_btn.click(fn=refresh_stats, outputs=stats_display)

        with gr.Row():
            with gr.Column():
                gr.Markdown("## 🔮 功能预览")
                gr.Markdown("""
                未来这里将包含：
                - 📈 详细的偏好分析图表
                - 🎯 个性化推荐设置
                - 📝 用户反馈收集
                - 🔧 模型参数调优界面
                - 📊 效果对比分析
                """)


def create_app():
    """创建主应用"""
    with gr.Blocks(
        title="FinPOC - 金融行业POC验证平台",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .tab-nav button {
            font-size: 16px !important;
            font-weight: 600 !important;
        }
        .result-markdown {
            max-height: 400px !important;
            overflow-y: auto !important;
            padding: 15px !important;
            border: 1px solid #e0e0e0 !important;
            border-radius: 8px !important;
            background-color: #fafafa !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            line-height: 1.6 !important;
        }
        .result-markdown h1, .result-markdown h2, .result-markdown h3 {
            color: #2E86AB !important;
            margin-top: 1em !important;
            margin-bottom: 0.5em !important;
        }
        .result-markdown p {
            margin-bottom: 1em !important;
        }
        .result-markdown ul, .result-markdown ol {
            margin-left: 1.5em !important;
            margin-bottom: 1em !important;
        }
        .result-markdown blockquote {
            border-left: 4px solid #2E86AB !important;
            padding-left: 1em !important;
            margin: 1em 0 !important;
            background-color: #f0f8ff !important;
        }
        .result-markdown code {
            background-color: #f4f4f4 !important;
            padding: 2px 4px !important;
            border-radius: 3px !important;
            font-family: 'Courier New', monospace !important;
        }
        .result-markdown pre {
            background-color: #f4f4f4 !important;
            padding: 10px !important;
            border-radius: 5px !important;
            overflow-x: auto !important;
        }
        """
    ) as app:

        gr.Markdown(
            """
            <div style="text-align: center; margin-bottom: 20px;">
                <h1 style="color: #2E86AB; margin-bottom: 10px;">🚀 FinPOC</h1>
                <h3 style="color: #666; margin-top: 0;">金融行业POC验证平台</h3>
            </div>
            """,
            elem_id="header"
        )

        with gr.Tabs() as tabs:
            with gr.Tab("🏠 主页", id="homepage"):
                create_homepage()

            with gr.Tab("📝 模版化写作", id="template_writing"):
                create_template_writing_interface()

            with gr.Tab("👍 偏好标注", id="preference_annotation"):
                create_preference_annotation_interface()

    return app


if __name__ == "__main__":
    # 创建应用
    app = create_app()

    # 启动应用
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        # show_tips=True,
        # enable_queue=True
    )
